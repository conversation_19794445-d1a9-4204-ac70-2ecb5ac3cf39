Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.AuthenticationProxy", "src\IT.Microservices.AuthenticationProxy.csproj", "{FC4FC75D-AADE-C06C-0C0E-BEC146A996A9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.AuthenticationProxy.UnitTests", "tests\IT.Microservices.AuthenticationProxy.UnitTests\IT.Microservices.AuthenticationProxy.UnitTests.csproj", "{072BE70A-F2F0-8B84-D244-531BCA8087A8}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FC4FC75D-AADE-C06C-0C0E-BEC146A996A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC4FC75D-AADE-C06C-0C0E-BEC146A996A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FC4FC75D-AADE-C06C-0C0E-BEC146A996A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC4FC75D-AADE-C06C-0C0E-BEC146A996A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{072BE70A-F2F0-8B84-D244-531BCA8087A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{072BE70A-F2F0-8B84-D244-531BCA8087A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{072BE70A-F2F0-8B84-D244-531BCA8087A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{072BE70A-F2F0-8B84-D244-531BCA8087A8}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{072BE70A-F2F0-8B84-D244-531BCA8087A8} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3B13AFA9-566D-457E-9265-A55B2BFBFB2B}
	EndGlobalSection
EndGlobal
