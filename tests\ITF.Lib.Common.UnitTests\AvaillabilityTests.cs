using ITF.Lib.Common.Availability;

namespace ITF.Lib.Common.UnitTests;

public class AvaillabilityTests
{


    [Fact]
    public void When_AllMandatoryFieldArePassed_ExpectObjectInstanciated()
    {
        // Act
        // Assert
        var availability = new AvailabilityBuilder()
            .WithDeliveryDate(DateTime.Now)
            .WithProductSKUs(new List<string> { "A", "B" })
            .WithProductSKU("A")
            .WithPostalCode("69000")
            .WithCountryCode("FR")
            .Build();

        Assert.IsType<AvailabilityInputStub>(availability);
    }

    [Fact]
    public void When_InstanceCreated_ExpectObjectImplementInterface()
    {
        // Act
        // Assert
        var availability = new AvailabilityBuilder()
            .WithDeliveryDate(DateTime.Now)
            .WithProductSKUs(new List<string> { "A", "B" })
            .WithProductSKU("A")
            .WithPostalCode("69000")
            .WithCountryCode("FR")
            .Build();
        Assert.IsAssignableFrom<IAvailabilityInput>(availability);
    }


    public class AvailabilityBuilder
    {
        public string ProductSKU { get; set; }
        public List<string> ProductSKUs { get; set; } = new();
        public string PostalCode { get; set; }
        public string CountryCode { get; set; }
        public DateTime DeliveryDate { get; set; }

        public AvailabilityBuilder()
        {
            ProductSKUs = new();
            ProductSKU = string.Empty;
            PostalCode = string.Empty;
            CountryCode = string.Empty;
            DeliveryDate = default;
        }

        public AvailabilityBuilder WithProductSKU(string productSKU)
        {
            ProductSKU = productSKU;
            return this;
        }

        public AvailabilityBuilder WithPostalCode(string postalCode)
        {
            PostalCode = postalCode;
            return this;
        }

        public AvailabilityBuilder WithCountryCode(string countryCode)
        {
            CountryCode = countryCode;
            return this;
        }

        public AvailabilityBuilder WithDeliveryDate(DateTime deliveryDate)
        {
            DeliveryDate = deliveryDate;
            return this;
        }

        public AvailabilityBuilder WithProductSKUs(List<string> productSKUs)
        {
            ProductSKUs = productSKUs;
            return this;
        }

        public AvailabilityInputStub Build()
        {
            return new AvailabilityInputStub(ProductSKU, ProductSKUs, PostalCode, CountryCode, DeliveryDate);
        }
    }

    public class AvailabilityInputStub : IAvailabilityInput
    {
        public AvailabilityInputStub(string productSKU, List<string> productSKUs, string postalCode, string countryCode, DateTime deliveryDate)
        {
            // Spe rules if any (not here)
        }

        public AvailabilityInputStub(string productSKU, List<string> productSKUs, string postalCode, string countryCode, DateTime deliveryDate, string city, string street, double? latitude, double? longitude, string orderId, DeliveryMoment? moment, bool? isFuneral, bool? hasRibbon) 
        {
            // Spe rules if any (not here)
        }

        public string ProductSKU { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public List<string> ProductSKUs { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public string ProductVariantSKU { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public List<string> ProductVariantSKUs { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public string PostalCode { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public string CountryCode { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public DateTime DeliveryDate { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public string City { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public string Street { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public double? Latitude { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public double? Longitude { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public string OrderId { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public DeliveryMoment? Moment { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public bool? IsFuneral { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public bool? HasRibbon { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public string Province { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public List<string>? FloristId { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public string? BusinessUnitId { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        public string? AddressId { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
    }
}
