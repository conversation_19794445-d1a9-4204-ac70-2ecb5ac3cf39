global using JsonException = System.Text.Json.JsonException;
global using System.Text.Json;

namespace IT.Microservices.AuthenticationProxy.ProxyAuth.Services;

public class JwtTokenService(ILogger<JwtTokenService> logger) : IJwtTokenService
{
    public Result<string, Error> ExtractIssuer(string token)
    {
        try
        {
            // Validate JWT format (should have 3 parts separated by dots)
            var parts = token.Split('.');
            if (parts.Length != 3)
            {
                logger.LogError("Invalid JWT format: expected 3 parts, got {Count}", parts.Length);
                return new Error("INVALID_TOKEN_FORMAT", "Invalid JWT format");
            }

            // Decode the payload (second part)
            var payload = parts[1];
            var decodedPayload = Base64UrlDecode(payload);
            
            if (string.IsNullOrEmpty(decodedPayload))
            {
                logger.LogError("Failed to decode JWT payload");
                return new Error("INVALID_TOKEN_FORMAT", "Failed to decode JWT payload");
            }

            // Parse JSON to extract issuer
            using var document = JsonDocument.Parse(decodedPayload);
            if (!document.RootElement.TryGetProperty("iss", out var issuerElement))
            {
                logger.LogError("Missing 'iss' claim in token payload");
                return new Error("INVALID_TOKEN_FORMAT", "Missing iss in token");
            }

            var issuer = issuerElement.GetString();
            if (string.IsNullOrEmpty(issuer))
            {
                logger.LogError("Empty 'iss' claim in token payload");
                return new Error("INVALID_TOKEN_FORMAT", "Empty iss in token");
            }

            return issuer;
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Failed to parse JWT payload as JSON");
            return new Error("INVALID_TOKEN_FORMAT", "Invalid JSON in token payload");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error while extracting issuer from token");
            return new Error("TOKEN_PROCESSING_ERROR", "Failed to process token");
        }
    }

    private static string Base64UrlDecode(string input)
    {
        try
        {
            // Add padding if necessary
            var remainder = input.Length % 4;
            if (remainder > 0)
            {
                input += new string('=', 4 - remainder);
            }

            // Replace URL-safe characters with standard base64 characters
            input = input.Replace('-', '+').Replace('_', '/');

            // Decode
            var bytes = Convert.FromBase64String(input);
            return System.Text.Encoding.UTF8.GetString(bytes);
        }
        catch (Exception)
        {
            return string.Empty;
        }
    }
}

public interface IJwtTokenService
{
    Result<string, Error> ExtractIssuer(string token);
}
