﻿using System.Collections.Generic;

namespace ITF.Lib.Common.Availability
{
    public interface IAutoCompleteOutput
    {
        public List<PlaceAutoComplete> Places { get; set; }
    }

    public class PlaceAutoComplete
    {
        public string? AddressId { get; set; }
        public string? ParishId { get; set; }
        public string? SettlementId { get; set; }
        public string? Name { get; set; }
        public string? PostalCode { get; set; }
        public string? Province { get; set; }
    }
}
