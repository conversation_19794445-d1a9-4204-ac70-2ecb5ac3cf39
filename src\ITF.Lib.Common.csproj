﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<Description>
			Interflora common interfaces and base class shared among other libs
			v 5.6.0 : Added AddressId into IAvailabilityInput + ParishId into IAvailabilityOutput
			v 5.5.0 : Add AddressId, ParishId, SettlementId to ITF.Lib.Common.Availability.PlaceAutoComplete
			v 5.4.1 : Add QualityFactor to IFloristDistance in IAvailabilityOutput
			v 5.4.0 : Add Florist Distance list in IAvailabilityOutput
			v 5.3.1 : GSEMIG-214 - Patch
			v 5.3.0 : GSEMIG-214 - Changed input IAvailabilityInput and IAvailabilityOutput
			v 5.2.1 : Improved output IAvailabilityOutput
			v 5.2.0 : Changed output IAvailabilityOutput
			v 5.1.0 : Changed input IAvailabilityInput and IUnavailableDaysInput
			v 5.0.0 : Update to .NET 8
			v 4.8.1 : Changed output of ValidateRegion
			v 4.8.0 : Add OneOf nuget lib
			v 4.7.0 : Update deps + enable Nullable check in csproj
			v 4.6.0 : FLR1376 - Add the phone number in the GetFlorists output DTO
			v 4.5.0 : Added IValidateRegionInput, IValidateRegionOutput and IAvailabilityUseCase.ValidateRegionCart
			v 4.4.0 : Added Evening value into Enum DeliveryMoment
			v 4.3.1 : Added Province field to IFloristsInput
			v 4.3.0 : Added IFloristsInput, IFloristsOutput, and IAvailabilityUseCase.GetFlorists
			v 4.2.0 : Add a default Error Class + add FunctionalExtensions lib
			v 4.1.1 : Merge commit v3.12.1 (Fix unit tests for Province field)
			v 4.1.0 : Merge commit v3.12.0 (Province field)
			v 4.0.0 : Target .net 7
			v 3.12.1 : Fix tests for Province field (availability)
			v 3.12.0 : Add Province for a bunch of dto (availability)
			v 3.11.0 : Store projected events : add method
			v 3.10.0 : Aggregate : supply event list as immutable
			v 3.9.0 : Provide IUndeliverableDays interfaces
			v 3.8.0 : Provide IAvailabilityOutput interface
			v 3.7.0 : Provide UnavailableDays interface
			v 3.6.0 : Provide auto complete interface
			v 3.5.0 : Provide get moments interface
			v 3.4.0 : Remove concrete implementations for availability / change interface contracts
			v 3.3.0 : Add Availability interface
			v 3.2.0 : Add MessageId in interface
			v 3.1.0 : Update test framework dep
			v 3.0.0 : Target .net 6
			v 2.1.0 : Enforce contracts for Kafka published messages
			v 2.0.0 : BREAKING CHANGES : remove TracingKeys/ remove ExpiryTimeGlobal / add causationId
			v 1.5.0 : Add base projected event class
			v 1.4.0 : Add get tracing extension method
			v 1.3.0 : Set Event from Aggregate private
			v 1.2.0 : Update DDD utils
			v 1.1.0 : Add DDD base class
			v 1.0.0 : First implementation
		</Description>
		<PackageReleaseNotes>
			v 5.6.0 : Added AddressId into IAvailabilityInput + ParishId into IAvailabilityOutput
			v 5.5.0 : Add AddressId, ParishId, SettlementId to ITF.Lib.Common.Availability.PlaceAutoComplete
			v 5.4.1 : Add QualityFactor to IFloristDistance in IAvailabilityOutput
			v 5.4.0 : Add Florist Distance list in IAvailabilityOutput
			v 5.3.1 : GSEMIG-214 - Patch
			v 5.3.0 : GSEMIG-214 - Changed input IAvailabilityInput and IAvailabilityOutput
			v 5.2.1 : Improved output IAvailabilityOutput
			v 5.2.0 : Changed output IAvailabilityOutput
			v 5.1.0 : Changed input IAvailabilityInput and IUnavailableDaysInput
			v 5.0.0 : Update to .NET 8
			v 4.8.1 : Changed output of ValidateRegion
			v 4.8.0 : Add OneOf nuget lib
			v 4.7.0 : Update deps + enable Nullable check in csproj
			v 4.6.0 : FLR1376 - Add the phone number in the GetFlorists output DTO
			v 4.5.0 : Added IValidateRegionInput, IValidateRegionOutput and IAvailabilityUseCase.ValidateRegionCart
			v 4.4.0 : Added Evening value into Enum DeliveryMoment
			v 4.3.1 : Added Province field to IFloristsInput
			v 4.3.0 : Added IFloristsInput, IFloristsOutput, and IAvailabilityUseCase.GetFlorists
			v 4.2.0 : Add a default Error Class + add FunctionalExtensions lib
			v 4.1.1 : Merge commit v3.12.1 (Fix unit tests for Province field)
			v 4.1.0 : Merge commit v3.12.0 (Province field)
			v 4.0.0 : Target .net 7
			v 3.12.1 : Fix tests for Province field (availability)
			v 3.12.0 : Add Province for a bunch of dto (availability)
			v 3.11.0 : Store projected events : add method
			v 3.10.0 : Aggregate : supply event list as immutable
			v 3.9.0 : Provide IUndeliverableDays interfaces
			v 3.8.0 : Provide IAvailabilityOutput interface
			v 3.7.0 : Provide UnavailableDays interface
			v 3.6.0 : Provide auto complete interface
			v 3.5.0 : Provide get moments interface
			v 3.4.0 : Remove concrete implementations for availability / change interface contracts
			v 3.3.0 : Add Availability interface
			v 3.2.0 : Add MessageId in interface
			v 3.1.0 : Update test framework dep
			v 3.0.0 : Target .net 6
			v 2.1.0 : Enforce contracts for Kafka published messages
			v 2.0.0 : BREAKING CHANGES : remove TracingKeys/ remove ExpiryTimeGlobal / add causationId
			v 1.5.0 : Add base projected event class
			v 1.4.0 : Add get tracing extension method
			v 1.3.0 : Set Event from Aggregate private
			v 1.2.0 : Update DDD utils
			v 1.1.0 : Add DDD base class
			v 1.0.0 : First implementation
		</PackageReleaseNotes>
		<PackageTags>Git on Azure Devops</PackageTags>
		<RepositoryUrl>https://interflorad365fo.visualstudio.com/ITF.Lib.Common/_git/ITF.Lib.Common</RepositoryUrl>
		<Copyright>Interflora France</Copyright>
		<Version>5.6.0</Version>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="CSharpFunctionalExtensions" Version="3.2.0" />
		<PackageReference Include="OneOf" Version="3.0.271" />
	</ItemGroup>
</Project>
