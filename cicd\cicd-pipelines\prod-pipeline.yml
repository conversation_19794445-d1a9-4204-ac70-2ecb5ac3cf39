trigger:
- master

pool:
  vmImage: ubuntu-latest

variables:
  #####################################
  ## common-variables
  #####################################

  projectName: 'itauthenticationproxy'

  #Use by csproj files to build and run unit tests
  dotnetProjectName: 'IT.Microservices.AuthenticationProxy'
  dotnetTestName: 'IT.Microservices.AuthenticationProxy.UnitTests'

  helmVersion: 3.7.1
  HELM_EXPERIMENTAL_OCI: '1'

  #Must match chart/Chart.yaml name
  helmChartName: 'itauthenticationproxy'

  #Helm Release name 
  helmReleaseName: 'itauthenticationproxy'

  #Use Azure devOps build id to tag image --> must be remplaced by commitID 
  imageTag: $(build.SourceVersion)

  #Registry name to store docker image and helm chart (create if not exist, must be lowercase)
  imageRepository: 'itauthenticationproxy'

  #####################################
  ##prod-variables
  #####################################

  #According to chart/Chart.yaml value   
  helmChartVersion: "1.4.6"
  
  containerRegistry: 'itfprodacr.azurecr.io'
  containerFullPath: '$(containerRegistry)/$(imageRepository)'
  containerRegistryLogin: 'itfprodacr'
  #containerRegistryPwd: 'must be defined in azure pipeline variables'
    
  #Azure service connection name(Project settings -> Service connection -> Azure Resource Manager)
  Azure.ServiceConnection: 'it-prod-cicd-sp'

  Azure.resourceGroup: 'itf-prod-k8s-rg'
  Azure.kubernetesClusterName: 'itf-prod-k8s-aks'

  #Shared Helm chart repository name (output from terraform)
  HelmChartRepository: 'itfsharedacr'
  HelmChartRepositoryClientID: "70085f74-d6cc-4bda-8cd1-bad624af5a20"
  #HelmChartRepositoryClientSecret: 'must be defined in azure pipeline variables'
  
  #Enable Azure devops debug mode
  System.Debug: 'false'

stages:
- stage: Build_Stage
  displayName: Build image
  jobs:  
  - job: Build_Job
    displayName: Build and push Docker/Helm image
    steps: 
    - template: templates/build.yml

- stage: Deploy_France
  dependsOn: Build_Stage
  displayName: Deploy itauthenticationproxy France (k8s)
  jobs: 
  - deployment: Deploy_France
    displayName: Deploy France app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-prod-france.yaml"
      K8S.Namespace: 'itf-ms'
    environment: prod-itf-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Italy
  dependsOn: Build_Stage
  displayName: Deploy itauthenticationproxy Italy (k8s)
  jobs: 
  - deployment: Deploy_Italy
    displayName: Deploy Italy app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-prod-italy.yaml"
      K8S.Namespace: 'iti-ms'
    environment: prod-iti-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Spain
  dependsOn: Build_Stage
  displayName: Deploy itauthenticationproxy Spain (k8s)
  jobs: 
  - deployment: Deploy_Spain
    displayName: Deploy Spain app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-prod-spain.yaml"
      K8S.Namespace: 'ite-ms'
    environment: prod-ite-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Portugal
  dependsOn: Build_Stage
  displayName: Deploy itauthenticationproxy Portugal (k8s)
  jobs: 
  - deployment: Deploy_Portugal
    displayName: Deploy Portugal app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-prod-portugal.yaml"
      K8S.Namespace: 'itp-ms'
    environment: prod-itp-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Sweden
  dependsOn: Build_Stage
  displayName: Deploy itauthenticationproxy Sweden (k8s)
  jobs: 
  - deployment: Deploy_Sweden
    displayName: Deploy Sweden app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-prod-sweden.yaml"
      K8S.Namespace: 'its-ms'
    environment: prod-its-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Denmark
  dependsOn: Build_Stage
  displayName: Deploy itauthenticationproxy Denmark (k8s)
  jobs: 
  - deployment: Deploy_Denmark
    displayName: Deploy Denmark app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-prod-denmark.yaml"
      K8S.Namespace: 'itd-ms'
    environment: prod-itd-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml