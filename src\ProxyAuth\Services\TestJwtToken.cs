global using JsonSerializer = System.Text.Json.JsonSerializer;

namespace IT.Microservices.AuthenticationProxy.ProxyAuth.Services;

/// <summary>
/// Simple test class to verify JWT token parsing functionality
/// </summary>
public static class TestJwtToken
{
    /// <summary>
    /// Creates a simple test JWT token for testing purposes
    /// </summary>
    public static string CreateTestToken(string issuer = "https://test-issuer.com")
    {
        // Create a simple JWT payload
        var payload = new
        {
            iss = issuer,
            sub = "test-user",
            exp = DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds(),
            iat = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
        };

        var payloadJson = JsonSerializer.Serialize(payload);
        var payloadBase64 = Base64UrlEncode(payloadJson);

        // Create a simple header
        var header = new { alg = "HS256", typ = "JWT" };
        var headerJson = JsonSerializer.Serialize(header);
        var headerBase64 = Base64UrlEncode(headerJson);

        // Create a dummy signature
        var signature = Base64UrlEncode("dummy-signature");

        return $"{headerBase64}.{payloadBase64}.{signature}";
    }

    private static string Base64UrlEncode(string input)
    {
        var bytes = System.Text.Encoding.UTF8.GetBytes(input);
        var base64 = Convert.ToBase64String(bytes);
        return base64.Replace('+', '-').Replace('/', '_').TrimEnd('=');
    }
}
