using IT.Microservices.AuthenticationProxy.ProxyAuth.Configuration;

namespace IT.Microservices.AuthenticationProxy.ProxyAuth.Services;

public class IssuerConfigurationService(
    ILogger<IssuerConfigurationService> logger,
    IOptionsMonitor<TokenIntrospectionConfiguration> configurationMonitor) : IIssuerConfigurationService
{

    public Task<Result<IssuerConfiguration, Error>> GetIssuerConfigurationAsync(string issuer)
    {
        if (string.IsNullOrWhiteSpace(issuer))
        {
            logger.LogError("Issuer cannot be null or empty");
            return Task.FromResult(Result.Failure<IssuerConfiguration, Error>(
                new Error("INVALID_ISSUER", "Issuer cannot be null or empty")));
        }

        var config = configurationMonitor.CurrentValue;

        if (config.Issuers.Count == 0)
        {
            logger.LogError("No issuers configured in appsettings");
            return Task.FromResult(Result.Failure<IssuerConfiguration, Error>(
                new Error("NO_ISSUERS_CONFIGURED", "No issuers configured")));
        }

        // Find issuer by matching the IssuerUrl
        var issuerConfig = config.Issuers.Values.FirstOrDefault(i => i.IssuerUrl == issuer);

        if (issuerConfig == null)
        {
            logger.LogError("Unknown issuer: {Issuer}", issuer);
            return Task.FromResult(Result.Failure<IssuerConfiguration, Error>(
                new Error("UNKNOWN_ISSUER", "Unknown issuer")));
        }

        // Convert to the expected IssuerConfiguration format
        var configuration = new IssuerConfiguration
        {
            IntrospectUrl = issuerConfig.IntrospectionEndpoint,
            ClientId = issuerConfig.ClientId,
            ClientSecret = issuerConfig.ClientSecret
        };

        logger.LogInformation("Found configuration for issuer: {Issuer}", issuer);
        return Task.FromResult(Result.Success<IssuerConfiguration, Error>(configuration));
    }

    public Task<Dictionary<string, IssuerConfiguration>> GetAllIssuersAsync()
    {
        var config = configurationMonitor.CurrentValue;
        var result = new Dictionary<string, IssuerConfiguration>();

        foreach (var kvp in config.Issuers)
        {
            result[kvp.Value.IssuerUrl] = new IssuerConfiguration
            {
                IntrospectUrl = kvp.Value.IntrospectionEndpoint,
                ClientId = kvp.Value.ClientId,
                ClientSecret = kvp.Value.ClientSecret
            };
        }

        return Task.FromResult(result);
    }
}

public interface IIssuerConfigurationService
{
    Task<Result<IssuerConfiguration, Error>> GetIssuerConfigurationAsync(string issuer);
    Task<Dictionary<string, IssuerConfiguration>> GetAllIssuersAsync();
}
