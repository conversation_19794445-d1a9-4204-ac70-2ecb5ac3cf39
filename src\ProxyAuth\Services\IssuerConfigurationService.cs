namespace IT.Microservices.AuthenticationProxy.ProxyAuth;

public class IssuerConfigurationService(ILogger<IssuerConfigurationService> logger) : IIssuerConfigurationService
{
    private readonly Dictionary<string, IssuerConfiguration> _issuers = LoadIssuersFromEnvironment();

    public Task<Result<IssuerConfiguration, Error>> GetIssuerConfigurationAsync(string issuer)
    {
        if (string.IsNullOrWhiteSpace(issuer))
        {
            logger.LogError("Issuer cannot be null or empty");
            return Task.FromResult(Result.Failure<IssuerConfiguration, Error>(
                new Error("INVALID_ISSUER", "Issuer cannot be null or empty")));
        }

        if (_issuers.Count == 0)
        {
            logger.LogError("No issuers configured via environment variables");
            return Task.FromResult(Result.Failure<IssuerConfiguration, Error>(
                new Error("NO_ISSUERS_CONFIGURED", "No issuers configured")));
        }

        if (!_issuers.TryGetValue(issuer, out var configuration))
        {
            logger.LogError("Unknown issuer: {Issuer}", issuer);
            return Task.FromResult(Result.Failure<IssuerConfiguration, Error>(
                new Error("UNKNOWN_ISSUER", "Unknown issuer")));
        }

        logger.LogInformation("Found configuration for issuer: {Issuer}", issuer);
        return Task.FromResult(Result.Success<IssuerConfiguration, Error>(configuration));
    }

    private static Dictionary<string, IssuerConfiguration> LoadIssuersFromEnvironment()
    {
        var issuers = new Dictionary<string, IssuerConfiguration>();

        // Get all environment variables
        var environmentVariables = Environment.GetEnvironmentVariables();

        // Find all ISSUER_* variables
        foreach (string key in environmentVariables.Keys)
        {
            if (key.StartsWith("ISSUER_", StringComparison.OrdinalIgnoreCase))
            {
                var name = key.Substring(7); // Remove "ISSUER_" prefix
                var issuerUrl = environmentVariables[key]?.ToString();

                if (string.IsNullOrWhiteSpace(issuerUrl))
                    continue;

                // Look for corresponding configuration variables
                var introspectKey = $"INTROSPECT_{name}";
                var clientIdKey = $"CLIENT_ID_{name}";
                var clientSecretKey = $"CLIENT_SECRET_{name}";

                var introspectUrl = Environment.GetEnvironmentVariable(introspectKey);
                var clientId = Environment.GetEnvironmentVariable(clientIdKey);
                var clientSecret = Environment.GetEnvironmentVariable(clientSecretKey);

                // Only add if all required configuration is present
                if (!string.IsNullOrWhiteSpace(introspectUrl) &&
                    !string.IsNullOrWhiteSpace(clientId) &&
                    !string.IsNullOrWhiteSpace(clientSecret))
                {
                    issuers[issuerUrl] = new IssuerConfiguration
                    {
                        IntrospectUrl = introspectUrl,
                        ClientId = clientId,
                        ClientSecret = clientSecret
                    };
                }
            }
        }

        return issuers;
    }

    public Task<Dictionary<string, IssuerConfiguration>> GetAllIssuersAsync()
    {
        return Task.FromResult(new Dictionary<string, IssuerConfiguration>(_issuers));
    }
}

public interface IIssuerConfigurationService
{
    Task<Result<IssuerConfiguration, Error>> GetIssuerConfigurationAsync(string issuer);
    Task<Dictionary<string, IssuerConfiguration>> GetAllIssuersAsync();
}
