﻿using commercetools.Sdk.Api.Models.Products;
using ITF.SharedModels.Group.Enums;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace ITF.SharedModels.DataModels.Order;

public class GlobalOrderModel
{
    public string DeliveryCountryCode { get; set; }
    public string SenderCountryCode { get; set; }
    public string Src { get; set; }
    public string SenderFloristIdentifier { get; set; }
    public string OrderNumber { get; set; }
    public string CtOrderId { get; set; }
    public decimal? OrderTotal { get; set; } = null;
    public string CurrencyCode { get; set; }
    public string CardMessage { get; set; }
    public DateTime CreatedAt { get; set; }
    public string Status { get; set; }
    public string LegacyOrderNumber { get; set; }
    public GlobalOrderBilling Billing { get; set; } = new();
    public GlobalOrderShipping Shipping { get; set; } = new();
    public List<GlobalOrderProduct> Products { get; set; } = new();

    // fields presents in Commerce Tools
    public string Signature { get; set; }
    public string OccasionCode { get; set; }
    public string IP { get; set; }
    public string Device { get; set; }
    public ContactTitleTypeEnum ContactTitle { get; set; }
    public string ContactFirstName { get; set; }
    public string ContactLastName { get; set; }

    public OrderTypeEnum OrderType { get; set; }
    public string ExecutingFloristIdentifier { get; set; }
    public string ExecutingFloristType { get; set; }
    public bool IsInvoiceRequested { get; set; }

    // executiong florist personal code 
    public string? CodeAP { get; set; }

    public bool Equals(GlobalOrderModel parameter)
    {
        if (parameter == null) return false;
        return (DeliveryCountryCode == parameter.DeliveryCountryCode &&
            SenderCountryCode == parameter.SenderCountryCode &&
            Src == parameter.Src &&
            SenderFloristIdentifier == parameter.SenderFloristIdentifier &&
            OrderNumber == parameter.OrderNumber &&
            CtOrderId == parameter.CtOrderId &&
            OrderTotal == parameter.OrderTotal &&
            CurrencyCode == parameter.CurrencyCode &&
            CardMessage == parameter.CardMessage &&
            CreatedAt == parameter.CreatedAt &&
            Status == parameter.Status &&
            LegacyOrderNumber == parameter.LegacyOrderNumber &&
            ExecutingFloristIdentifier == parameter.ExecutingFloristIdentifier &&
            ExecutingFloristType == parameter.ExecutingFloristType &&
            Billing.Equals(parameter.Billing) &&
            Shipping.Equals(parameter.Shipping) &&
            Products.Equals(parameter.Products)
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as GlobalOrderModel);
    }

    public override int GetHashCode() => new
    {
        DeliveryCountryCode,
        Src,
        SenderFloristIdentifier,
        OrderNumber,
        CtOrderId,
        OrderTotal,
        CurrencyCode,
        CardMessage,
        CreatedAt,
        LegacyOrderNumber,
        Status,
        Billing,
        Shipping,
        Products,
        ExecutingFloristIdentifier,
        ExecutingFloristType
    }.GetHashCode();

    public static implicit operator GlobalOrderModel(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderCreatedMessage v)
    {
        var globalOrderModel = new GlobalOrderModel
        {
            CardMessage = v?.Payload?.CardMessage,
            CreatedAt = v?.Payload?.CreatedAt ?? DateTime.MinValue,
            CtOrderId = v?.Payload?.CtOrderId,
            CurrencyCode = v?.Payload?.CurrencyCode,
            DeliveryCountryCode = v?.Payload?.DeliveryContryCode,
            LegacyOrderNumber = v?.Payload?.LegacyOrderNumber,
            OrderNumber = v?.Payload?.OrderNumber,
            OrderTotal = v?.Payload?.OrderTotal,
            SenderCountryCode = v?.Payload?.SenderCountryCode,
            SenderFloristIdentifier = v?.Payload?.SenderFloristIdentifier,
            ExecutingFloristIdentifier = v?.Payload?.ExecutingFloristIdentifier,
            ExecutingFloristType = v?.Payload?.ExecutingFloristType,
            Src = v?.Payload?.Src,
            Status = v?.Payload?.Status,
            ContactFirstName = v?.Payload?.Shipping.FirstName,
            ContactLastName = v?.Payload?.Shipping.LastName,
            ContactTitle = Enum.TryParse(v?.Payload?.ContactTitle.ToUpper(), out ContactTitleTypeEnum contactTitleTypeEnum) ? contactTitleTypeEnum : ContactTitleTypeEnum.MRS,
            Signature = v?.Payload?.Signature,
            Device = v?.Payload?.SalesOrigin.ToString() ?? "UNKNOWN"
        };
        if (v?.Payload?.Billing != null)
        {
            globalOrderModel.Billing.Address = v?.Payload?.Billing.Address;
            globalOrderModel.Billing.FiscalCode = v?.Payload?.Billing.FiscalCode;
            globalOrderModel.Billing.CareOf = v?.Payload?.Billing.CareOf;
            globalOrderModel.Billing.City = v?.Payload?.Billing.City;
            globalOrderModel.Billing.CompanyName = v?.Payload?.Billing.CompanyName;
            globalOrderModel.Billing.CountryCode = v?.Payload?.Billing.CountryCode;
            globalOrderModel.Billing.Email = v?.Payload?.Billing.Email;
            globalOrderModel.Billing.FirstName = v?.Payload?.Billing.FirstName;
            globalOrderModel.Billing.FiscalCode = v?.Payload?.Billing.FiscalCode;
            globalOrderModel.Billing.LastName = v?.Payload?.Billing.LastName;
            globalOrderModel.Billing.Mobile = v?.Payload?.Billing.Mobile;
            globalOrderModel.Billing.ZipCode = v?.Payload?.Billing.ZipCode;

        }
        if (v?.Payload?.Shipping != null)
        {
            globalOrderModel.Shipping.StreetName = v?.Payload?.Shipping.StreetName;
            globalOrderModel.Shipping.StreetNumber = v?.Payload?.Shipping.StreetNumber;
            globalOrderModel.Shipping.DeliveryDate = v?.Payload?.Shipping.DeliveryDate ?? DateTime.MinValue;
            globalOrderModel.Shipping.City = v?.Payload?.Shipping.City;
            globalOrderModel.Shipping.Moment = v?.Payload?.Shipping.Moment ?? MomentEnum.Wholeday;
            globalOrderModel.Shipping.CountryCode = v?.Payload?.Shipping.CountryCode;
            globalOrderModel.Shipping.Province = v?.Payload?.Shipping.Province;
            globalOrderModel.Shipping.Email = v?.Payload?.Shipping.Email;
            globalOrderModel.Shipping.FirstName = v?.Payload?.Shipping.FirstName;
            globalOrderModel.Shipping.Time = v?.Payload?.Shipping.Time;
            globalOrderModel.Shipping.LastName = v?.Payload?.Shipping.LastName;
            globalOrderModel.Shipping.Mobile = v?.Payload?.Shipping.Mobile;
            globalOrderModel.Shipping.ZipCode = v?.Payload?.Shipping.ZipCode;
            globalOrderModel.Shipping.Longitude = v?.Payload?.Shipping.Longitude ?? 0;
            globalOrderModel.Shipping.Latitude = v?.Payload?.Shipping.Latitude ?? 0;
            globalOrderModel.Shipping.CompanyName = v?.Payload?.Shipping.CompanyName ?? "";
            globalOrderModel.Shipping.AdditionalAddressInfo = v?.Payload?.Notes ?? "";
            globalOrderModel.Shipping.Comments = v?.Payload?.Shipping.Comments ?? "";
        }
        if (v?.Payload?.Products?.Count > 0)
        {
            foreach (var product in v?.Payload?.Products)
            {
                globalOrderModel.Products.Add(new GlobalOrderProduct
                {
                    Description = product.Description,
                    Composition = product.Description, // FOR PSM FR needs
                    IsAccessoryFor = product.IsAccessoryFor,
                    MarketingFee = product.MarketingFee,
                    Name = product.Name,
                    Price = product.Price,
                    ProductKey = product.ProductKey,
                    Quantity = product.Quantity,
                    RibbonText = product.RibbonText,
                    VariantKey = product.VariantKey,
                    IsExternalPrice = product.IsExternalPrice,
                    Location = product.Location,
                    BundleId = product.BundleId,
                    ExecutingFloristAmount = product.ExecutingFloristAmount,
                    Type = product.Type
                    
                });
            }
        }
        return globalOrderModel;
    }
}

public static class GlobalOrderProductExtension
{
    public static bool IsDiscountLineItem(this GlobalOrderProduct product) => product.ProductKey.StartsWith("REM");
    public static bool IsBundlePart(this GlobalOrderProduct product) => !string.IsNullOrWhiteSpace(product.BundleId);
}

public static class GlobalOrderProductListExtension
{
   
    public static List<GlobalOrderProduct> GetLineItemsProducts(this List<GlobalOrderProduct> products)
    {
        var productsResult = new List<GlobalOrderProduct>();
        foreach (var product in products ?? new())
        {
            if (product != null && !product.ProductKey.StartsWith("FDL", StringComparison.OrdinalIgnoreCase))
            {
                if (product?.ProductKey == "IT")
                {
                    product.ProductKey = "ITFPLUS";
                }
                productsResult.Add(product ?? new());
            }

        }

        return productsResult;
    }
    public static List<GlobalOrderProduct> GroupByBundleProducts(this List<GlobalOrderProduct> products)
    {
        var productsResult = new List<GlobalOrderProduct>();
        var bundleIds = products.Where(p => p.IsBundlePart()).Select(p => p.BundleId).Distinct().ToList();
        foreach (var bundleId in bundleIds ?? new())
        {
            productsResult.Add(products.MapLineItemtoBundleProduct(bundleId));
        }
        productsResult.AddRange(products.FindAll(p => !p.IsBundlePart()));
        return productsResult;
    }

    public static GlobalOrderProduct MapLineItemtoBundleProduct(this List<GlobalOrderProduct> products, string bundleId)
    {
        var bundleProducts = products.FindAll(p => p.BundleId == bundleId);
        return new GlobalOrderProduct
        {
            ProductKey = bundleId,
            VariantKey = bundleId,
            BundleId = bundleId,
            Quantity = 1,
            MarketingFee = bundleProducts.Sum(p => !p.IsDiscountLineItem() ? p.MarketingFee : 0),
            Price = bundleProducts.Sum(p => !p.IsDiscountLineItem() ? p.Price : -p.Price),
            ExecutingFloristAmount = bundleProducts.Sum(p => !p.IsDiscountLineItem() ? p.ExecutingFloristAmount : -p.ExecutingFloristAmount)
        };
    }
}

public class GlobalOrderBilling
    {
        public ContactTitleTypeEnum BillingTitle { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string InvoiceFirstName { get; set; }
        public string InvoiceLastName { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string? StreetNumber { get; set; }
        public string? Province { get; set; }
        public string City { get; set; }
        public string ZipCode { get; set; }
        public string CountryCode { get; set; }
        public string Mobile { get; set; }
        public string FiscalCode { get; set; }
        public string CompanyName { get; set; }
        public string CompanyNumber { get; set; } = string.Empty;
        public string VatNumber { get; set; } = string.Empty;
        public string Pec { get; set; } = string.Empty;
        public string? AdditionalStreetInfo { get; set; }
        public string CareOf { get; set; } // address 2

        public bool Equals(GlobalOrderBilling parameter)
        {
            return (FirstName == parameter.FirstName &&
                LastName == parameter.LastName &&
                Email == parameter.Email &&
                Address == parameter.Address &&
                City == parameter.City &&
                ZipCode == parameter.ZipCode &&
                CountryCode == parameter.CountryCode &&
                Mobile == parameter.Mobile &&
                FiscalCode == parameter.FiscalCode &&
                CompanyName == parameter.CompanyName &&
                StreetNumber == parameter.StreetNumber &&
                AdditionalStreetInfo == parameter.AdditionalStreetInfo &&
                Province == parameter.Province &&
                CareOf == parameter.CareOf
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GlobalOrderBilling);
        }


        public override int GetHashCode() => new
        {
            FirstName,
            LastName,
            Email,
            Address,
            City,
            ZipCode,
            CountryCode,
            Mobile,
            FiscalCode,
            CompanyName,
            StreetNumber, 
            AdditionalStreetInfo, 
            Province,
            CareOf,
        }.GetHashCode();
    }

    public class GlobalOrderShipping
    {
        public ContactTitleTypeEnum ShippingTitle { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string StreetName { get; set; }
        public string? StreetNumber { get; set; }
        public string City { get; set; }
        public string ZipCode { get; set; }
        public string CountryCode { get; set; }
        public string? Province { get; set; }
        public string Mobile { get; set; }
        public DateTime DeliveryDate { get; set; }
        public MomentEnum Moment { get; set; }
        public string Time { get; set; } // HH:mm
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string? AdditionalAddressInfo { get; set; }
        public string? AdditionalStreetInfo { get; set; }
        public string CompanyName { get; set; }
        public string Comments { get; set; } // delivery instruction
        public DeliveryModeEnum DeliveryMode { get; set; }

        public bool Equals(GlobalOrderShipping parameter)
        {
            return (FirstName == parameter.FirstName &&
                LastName == parameter.LastName &&
                Email == parameter.Email &&
                StreetName == parameter.StreetName &&
                StreetNumber == parameter.StreetNumber &&
                City == parameter.City &&
                ZipCode == parameter.ZipCode &&
                CountryCode == parameter.CountryCode &&
                Province == parameter.Province &&
                Mobile == parameter.Mobile &&
                DeliveryDate == parameter.DeliveryDate &&
                Moment == parameter.Moment &&
                Time == parameter.Time &&
                Longitude == parameter.Longitude &&
                Latitude == parameter.Latitude &&
                Comments == parameter.Comments &&
                AdditionalAddressInfo == parameter.AdditionalAddressInfo
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GlobalOrderShipping);
        }


        public override int GetHashCode() => new
        {
            FirstName,
            LastName,
            Email,
            StreetName,
            StreetNumber,
            City,
            ZipCode,
            CountryCode,
            Province,
            Mobile,
            DeliveryDate,
            Moment,
            Time,
            Longitude,
            Latitude,
            Comments
        }.GetHashCode();
    }

    public class GlobalOrderProduct
    {
        public string Name { get; set; }
        public string ProductKey { get; set; }
        public string VariantKey { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public string? Description { get; set; }
        public string? Composition { get; set; }
        public string IsAccessoryFor { get; set; }
        public string RibbonText { get; set; }
        public decimal? MarketingFee { get; set; } = null;
        public string? TaxCategoryKey { get; set; }
        public bool IsExternalPrice { get; set; }
        public string BundleId { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public decimal? ExecutingFloristAmount { get; set; }
        public string? Type { get; set; }
    public bool Equals(GlobalOrderProduct parameter)
        {
            return (Name == parameter.Name &&
                ProductKey == parameter.ProductKey &&
                VariantKey == parameter.VariantKey &&
                Quantity == parameter.Quantity &&
                Price == parameter.Price &&
                Description == parameter.Description &&
                Composition == parameter.Composition &&
                IsAccessoryFor == parameter.IsAccessoryFor &&
                RibbonText == parameter.RibbonText &&
                MarketingFee == parameter.MarketingFee &&
                TaxCategoryKey == parameter.TaxCategoryKey &&
                IsExternalPrice == parameter.IsExternalPrice &&
                Location == parameter.Location &&
                BundleId == parameter.BundleId &&
                ExecutingFloristAmount == parameter.ExecutingFloristAmount &&
                Type == parameter.Type
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GlobalOrderProduct);
        }


        public override int GetHashCode() => new
        {
            Name,
            ProductKey,
            VariantKey,
            Quantity,
            Price,
            Description,
            Composition,
            IsAccessoryFor,
            RibbonText,
            MarketingFee,
            TaxCategoryKey,
            IsExternalPrice,
            BundleId,
            Location,
            ExecutingFloristAmount,
            Type,
        }.GetHashCode();

    }

