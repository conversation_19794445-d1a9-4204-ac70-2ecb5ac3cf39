# See https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/test/publish-test-results?view=azure-devops&tabs=yaml#docker
# V.C 15/05/2024

FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /src

COPY ["src/IT.Microservices.AuthenticationProxy.csproj", "/src/IT.Microservices.AuthenticationProxy/"]
COPY ["src/NuGet.config", "/src/IT.Microservices.AuthenticationProxy/"]

RUN dotnet restore "IT.Microservices.AuthenticationProxy/IT.Microservices.AuthenticationProxy.csproj"

WORKDIR "/src/IT.Microservices.AuthenticationProxy"

COPY . .

RUN dotnet build "src/IT.Microservices.AuthenticationProxy.csproj" -c Release
RUN dotnet test "tests/IT.Microservices.AuthenticationProxy.UnitTests/IT.Microservices.AuthenticationProxy.UnitTests.csproj" -c Release --logger "trx;LogFileName=testresults.trx"; exit 0
RUN dotnet publish "src/IT.Microservices.AuthenticationProxy.csproj" -c Release -o out

ENTRYPOINT sleep 10000