using System.Diagnostics;
using IT.Microservices.AuthenticationProxy.ProxyAuth.Configuration;

namespace IT.Microservices.AuthenticationProxy.ProxyAuth.Services;

public class TokenIntrospectionService(
    ILogger<TokenIntrospectionService> logger,
    IHttpClientFactory httpClientFactory,
    IOptionsMonitor<TokenIntrospectionConfiguration> configurationMonitor) : ITokenIntrospectionService
{
    public async Task<Result<TokenIntrospectionResponse, Error>> IntrospectTokenAsync(
        string token, 
        IssuerConfiguration config)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var configuration = configurationMonitor.CurrentValue;
            using var httpClient = httpClientFactory.CreateClient();

            // Configure timeout from configuration
            httpClient.Timeout = TimeSpan.FromSeconds(configuration.Timeouts.IntrospectionTimeoutSeconds);

            // Prepare the request
            var requestContent = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("token", token)
            });

            // Set Basic authentication header
            var credentials = Convert.ToBase64String(
                System.Text.Encoding.UTF8.GetBytes($"{config.ClientId}:{config.ClientSecret}"));
            httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);

            logger.LogInformation("Calling introspection endpoint: {Url}", config.IntrospectUrl);

            // Make the request
            var response = await httpClient.PostAsync(config.IntrospectUrl, requestContent);
            
            stopwatch.Stop();
            var elapsedMs = stopwatch.Elapsed.TotalMilliseconds;

            logger.LogInformation("Introspection response received: HTTP {StatusCode}, Time: {ElapsedMs}ms", 
                (int)response.StatusCode, elapsedMs);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                logger.LogError("Introspection request failed: HTTP {StatusCode}, Content: {Content}", 
                    (int)response.StatusCode, errorContent);
                
                return new Error("INTROSPECTION_FAILED",
                    $"Introspection request failed with status {response.StatusCode}");
            }

            // Parse the response
            var responseContent = await response.Content.ReadAsStringAsync();
            
            try
            {
                var introspectionResponse = JsonSerializer.Deserialize<TokenIntrospectionResponse>(
                    responseContent, 
                    new JsonSerializerOptions 
                    { 
                        PropertyNameCaseInsensitive = true 
                    });

                if (introspectionResponse == null)
                {
                    logger.LogError("Failed to deserialize introspection response");
                    return new Error("INVALID_RESPONSE", "Invalid introspection response");
                }

                // Log the result
                if (introspectionResponse.Active)
                {
                    logger.LogInformation("Token is active, subject: {Subject}", introspectionResponse.Sub);
                }
                else
                {
                    logger.LogInformation("Token is inactive");
                }

                return introspectionResponse;
            }
            catch (JsonException ex)
            {
                logger.LogError(ex, "Failed to parse introspection response JSON: {Content}", responseContent);
                return new Error("INVALID_RESPONSE", "Invalid JSON in introspection response");
            }
        }
        catch (HttpRequestException ex)
        {
            stopwatch.Stop();
            logger.LogError(ex, "HTTP error during introspection request: {Message}", ex.Message);
            return new Error("HTTP_ERROR", $"HTTP error: {ex.Message}");
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            stopwatch.Stop();
            logger.LogError(ex, "Introspection request timed out");
            return new Error("TIMEOUT", "Introspection request timed out");
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            logger.LogError(ex, "Unexpected error during introspection request");
            return new Error("INTROSPECTION_ERROR", "Unexpected error during introspection");
        }
    }
}

public interface ITokenIntrospectionService
{
    Task<Result<TokenIntrospectionResponse, Error>> IntrospectTokenAsync(string token, IssuerConfiguration config);
}
