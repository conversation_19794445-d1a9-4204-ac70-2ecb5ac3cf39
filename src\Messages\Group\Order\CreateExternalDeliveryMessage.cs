﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Messages.Italy;

namespace ITF.SharedModels.Messages.Group.Order
{

    public static partial class Messages
    {
        public static partial class V1
        {
            public class CreateExternalDeliveryMessage : BaseMessage<CreateExternalDeliveryPayload>, IMessageKey, IDistributedTracing
            {
                public string OrderId { 
                    get
                    {
                        return this?.Payload?.OrderId ?? string.Empty;
                    } 
                }

                public string GetMessageKey()
                    => Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public CreateExternalDeliveryMessage() { }

                public CreateExternalDeliveryMessage(string deliveryServiceProviderId, string orderId, string floristId, double deliveryPrice)
                {
                    if (this.Payload == null)
                    {
                        this.Payload = new();
                    }
                    this.Payload.DeliveryServiceProviderId = deliveryServiceProviderId;
                    this.Payload.OrderId = orderId;
                    this.Payload.FloristId = floristId;
                    this.Payload.DeliveryPrice = deliveryPrice;

                }
            }
        }
    }

    public class CreateExternalDeliveryPayload : LegacyPayload, IEquatable<CreateExternalDeliveryPayload>
    {
        public string DeliveryServiceProviderId { get; set; }
        public string OrderId { get; set; }
        public string FloristId { get; set; }
        public double DeliveryPrice { get; set; } = 0;
        public bool Equals(CreateExternalDeliveryPayload parameter)
        {
            if (parameter == null) return false;
            return (DeliveryServiceProviderId == parameter.DeliveryServiceProviderId &&
                    OrderId == parameter.OrderId &&
                    FloristId == parameter.FloristId &&
                    DeliveryPrice == parameter.DeliveryPrice
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as CreateExternalDeliveryPayload);
        }

        public override int GetHashCode() => new
        {
            DeliveryServiceProviderId,
            OrderId,
            FloristId,
            DeliveryPrice
        }.GetHashCode();
    }
}
