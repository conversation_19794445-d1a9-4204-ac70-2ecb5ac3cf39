﻿namespace ITF.Lib.Common.Availability
{
    public interface IAvailabilityInput
    {
        // Mandatory field
        public string ProductSKU { get; set; }
        public List<string> ProductSKUs { get; set; }
        public string PostalCode { get; set; }
        public string CountryCode { get; set; }
        public DateTime DeliveryDate { get; set; }

        // Optional fields
        public string ProductVariantSKU { get; set; }
        public List<string> ProductVariantSKUs { get; set; }
        public string City { get; set; }
        public string Street { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public string OrderId { get; set; }
        public DeliveryMoment? Moment { get; set; }
        public bool? IsFuneral { get; set; }
        public bool? HasRibbon { get; set; }
        public string Province { get; set; }
        public List<string>? FloristId { get; set; }
        public string? BusinessUnitId { get; set; }
        public string? AddressId { get; set; }
    }
    public enum DeliveryMoment
    {
        Morning,
        Afternoon,
        Wholeday,
        Evening
    }
}
