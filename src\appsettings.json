{
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Information",
        "Elastic": "Warning",
        "Apm": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "Properties": {
      "ApplicationName": "IT.Microservices.AuthenticationProxy"
    }
  },
  "ElasticApm": {
    "ServerUrl": "**********",
	"Enabled": true,
    "TransactionSampleRate": 1,
    "CaptureBody": "all",
    "CaptureHeaders": true,
    "SpanFramesMinDuration": 0, // no stacktrace except for exception
    "CloudProvider": "none"
  },
  "ElasticSearchLog": {
    "ElasticSearchLog": "**********"
  },
  "Unleash": {
    "Url": "**********",
    "ProjectId": "default",
    "ApplicationName": "IT.Microservices.AuthenticationProxy",
    "FetchTogglesIntervalInSeconds": 15,
    "SendMetricsIntervalInSeconds": 30,
    "Environment": "development"
  },
  "FeatureFlags": {
    "Provider": "featuremanager"
  },
  "TokenIntrospection": {
    "Timeouts": {
      "IntrospectionTimeoutSeconds": 5,
      "HttpClientTimeoutSeconds": 30
    },
    "Issuers": {
      "keycloak-prod": {
        "IssuerUrl": "https://keycloak.example.com/realms/production",
        "IntrospectionEndpoint": "https://keycloak.example.com/realms/production/protocol/openid-connect/token/introspect",
        "ClientId": "introspection-client",
        "ClientSecret": "your-production-secret"
      }
    }
  }
}
