# Example environment variables for token introspection
# Copy this file to .env and update with your actual values

# Keycloak Instance 1
ISSUER_KEYCLOAK1=https://keycloak.example.com/realms/myrealm
INTROSPECT_KEYCLOAK1=https://keycloak.example.com/realms/myrealm/protocol/openid-connect/token/introspect
CLIENT_ID_KEYCLOAK1=introspection-client
CLIENT_SECRET_KEYCLOAK1=your-client-secret-here

# Keycloak Instance 2 (if you have multiple)
ISSUER_KEYCLOAK2=https://auth.anotherdomain.com/realms/anotherrealm
INTROSPECT_KEYCLOAK2=https://auth.anotherdomain.com/realms/anotherrealm/protocol/openid-connect/token/introspect
CLIENT_ID_KEYCLOAK2=another-client
CLIENT_SECRET_KEYCLOAK2=another-secret-here

# You can add more issuers by following the same pattern:
# ISSUER_<NAME>=<issuer-url>
# INTROSPECT_<NAME>=<introspection-endpoint-url>
# CLIENT_ID_<NAME>=<client-id>
# CLIENT_SECRET_<NAME>=<client-secret>
