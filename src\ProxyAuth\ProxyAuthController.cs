using System.Text.Json.Serialization;

namespace IT.Microservices.AuthenticationProxy.ProxyAuth;

public record TokenIntrospectionResponse
{
    [JsonPropertyName("active")]
    public bool Active { get; init; } = false;

    [JsonPropertyName("sub")]
    public string? Sub { get; init; }

    [JsonPropertyName("iss")]
    public string? Iss { get; init; }

    [JsonPropertyName("exp")]
    public long? Exp { get; init; }

    [JsonPropertyName("iat")]
    public long? Iat { get; init; }

    [JsonPropertyName("client_id")]
    public string? ClientId { get; init; }

    [JsonPropertyName("username")]
    public string? Username { get; init; }

    [JsonPropertyName("scope")]
    public string? Scope { get; init; }

    [JsonPropertyName("error")]
    public string? Error { get; init; }
}

public record IssuerConfiguration
{
    public string IntrospectUrl { get; init; } = string.Empty;
    public string ClientId { get; init; } = string.Empty;
    public string ClientSecret { get; init; } = string.Empty;
}

public class ProxyAuthController(ILogger<ProxyAuthController> logger, IProxyAuthUseCase useCase) : BaseController
{
    [SwaggerOperation(
        Summary = "Token Introspection",
        Description = "Introspects a JWT token using the appropriate issuer's introspection endpoint",
        OperationId = "TokenIntrospection")]
    [SwaggerResponse(200, "Token introspection result", typeof(TokenIntrospectionResponse))]
    [SwaggerResponse(400, "Invalid request", typeof(TokenIntrospectionResponse))]
    [SwaggerResponse(401, "Unauthorized", typeof(TokenIntrospectionResponse))]
    [SwaggerResponse(500, "Internal server error", typeof(TokenIntrospectionResponse))]
    [HttpPost("")]
    public async Task<IActionResult> Introspect()
    {
        var authHeader = Request.Headers.Authorization.FirstOrDefault();

        if (string.IsNullOrEmpty(authHeader))
        {
            logger.LogError("Missing Authorization header");
            return Unauthorized(new TokenIntrospectionResponse
            {
                Active = false,
                Error = "Missing or invalid Authorization header"
            });
        }

        logger.LogInformation("Token introspection request received");

        var result = await useCase.ProcessAsync(authHeader);

        return result.IsSuccess
            ? Ok(result.Value)
            : result.Error.Code.ToString() switch
            {
                "INVALID_TOKEN_FORMAT" => BadRequest(new TokenIntrospectionResponse { Active = false, Error = result.Error.Message }),
                "UNKNOWN_ISSUER" => Unauthorized(new TokenIntrospectionResponse { Active = false, Error = result.Error.Message }),
                "NO_ISSUERS_CONFIGURED" => StatusCode(500, new TokenIntrospectionResponse { Active = false, Error = result.Error.Message }),
                _ => StatusCode(500, new TokenIntrospectionResponse { Active = false, Error = "Internal server error" })
            };
    }
}
