namespace IT.Microservices.AuthenticationProxy.ProxyAuth;

public record ProxyAuthInstropectionRequest(string FirstName = "", string LastName = "");

public record ProxyAuthResponse
{
    public string FullName { get; init; } = string.Empty;
    public bool IsValid { get; init; } = false;
}

public class MyFeatureController(ILogger<MyFeatureController> logger , IProxyAuthUseCase myUseCase) : BaseController
{
    [SwaggerOperation(
        Summary = "",
        Description = "",
        OperationId = "Proxy Auth Introspection token")]
    [SwaggerResponse(200, "", typeof(ProxyAuthResponse))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("")]
    public IActionResult MyFeature([FromBody] ProxyAuthInstropectionRequest req)
    {
        logger.LogWarning("Request introspection received : {req}", req.Serialize());
        var res = myUseCase.Process(req);
        return Ok(new ProxyAuthResponse { 
            FullName = req.FirstName + req.LastName ,
            IsValid = res.IsSuccess && res.Value
        });
    }
}
