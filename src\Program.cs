using IT.Microservices.AuthenticationProxy.ProxyAuth;
using IT.Microservices.AuthenticationProxy.ProxyAuth.Configuration;
using IT.Microservices.AuthenticationProxy.ProxyAuth.Services;

var builder = WebApplication.CreateBuilder(args);

builder.Host.ConfigureHostBuilder(); // Global Inteflora Config on the Host (Logging / Config)

//** Registering Services Part **
// Configure TokenIntrospection settings
builder.Services.Configure<TokenIntrospectionConfiguration>(
    builder.Configuration.GetSection(TokenIntrospectionConfiguration.SectionName));

builder.Services
    .AddHealthChecksMiddleware()
    .AddAllMetrics()
    .UseFeatureFlags(builder.Configuration)
    .AddSwagger(Assembly.GetExecutingAssembly().GetName().Name!)
    .AddScoped<IProxyAuthUseCase, ProxyAuthUseCase>()
    .AddScoped<IJwtTokenService, JwtTokenService>()
    .AddSingleton<IIssuerConfigurationService, IssuerConfigurationService>()
    .AddScoped<ITokenIntrospectionService, TokenIntrospectionService>()
    .AddHttpClient();

builder.Services
    .AddControllers()
    .UsePascalCase()
    .SuppressAutoACR();

var app = builder.Build();

// ** runtime configure part **
if(app.Environment.IsDevelopment())
    app.UseDeveloperExceptionPage();

app.UseSwaggerEndpoint(app.Services.GetRequiredService<IApiVersionDescriptionProvider>(), "itauthenticationproxy");

app.UseRouting();

// Metrics middleware
app.UseAllMetricsMiddleware()
    .UseMiddleware<RequestMiddleware>();

app.UseAuthorization();

app.UseHealthChecks();
app.UseReadynessRoute();
app.MapMetrics();
app.MapControllers();

await app.RunAsync();

