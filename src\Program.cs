using IT.Microservices.AuthenticationProxy.ProxyAuth;

var builder = WebApplication.CreateBuilder(args);

builder.Host.ConfigureHostBuilder(); // Global Inteflora Config on the Host (Logging / Config)

//** Registering Services Part **
builder.Services
    .AddHealthChecksMiddleware()
    .AddAllMetrics()
    .UseFeatureFlags(builder.Configuration)
    .AddSwagger(Assembly.GetExecutingAssembly().GetName().Name!)
    .AddScoped<IProxyAuthUseCase,ProxyAuthUseCase>();

builder.Services
    .AddControllers()
    .UsePascalCase()
    .SuppressAutoACR();

var app = builder.Build();

// ** runtime configure part **
if(app.Environment.IsDevelopment())
    app.UseDeveloperExceptionPage();

app.UseSwaggerEndpoint(app.Services.GetRequiredService<IApiVersionDescriptionProvider>(), "itauthenticationproxy");

app.UseRouting();

// Metrics middleware
app.UseAllMetricsMiddleware()
    .UseMiddleware<RequestMiddleware>();

app.UseAuthorization();

app.UseHealthChecks();
app.UseReadynessRoute();
app.MapMetrics();
app.MapControllers();

await app.RunAsync();

