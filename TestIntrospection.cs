using IT.Microservices.AuthenticationProxy.ProxyAuth;
using Microsoft.Extensions.Logging;

// Simple test to verify JWT parsing works
var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<JwtTokenService>();
var jwtService = new JwtTokenService(logger);

// Test with a valid JWT token
var testToken = TestJwtToken.CreateTestToken("https://keycloak.example.com/realms/test");
Console.WriteLine($"Test token: {testToken}");

var result = jwtService.ExtractIssuer(testToken);
if (result.IsSuccess)
{
    Console.WriteLine($"✅ Successfully extracted issuer: {result.Value}");
}
else
{
    Console.WriteLine($"❌ Failed to extract issuer: {result.Error.Message}");
}

// Test with invalid token
var invalidToken = "invalid.token";
var invalidResult = jwtService.ExtractIssuer(invalidToken);
if (invalidResult.IsFailure)
{
    Console.WriteLine($"✅ Correctly rejected invalid token: {invalidResult.Error.Message}");
}
else
{
    Console.WriteLine($"❌ Should have rejected invalid token");
}

Console.WriteLine("JWT parsing test completed!");
