<?xml version="1.0"?>
<doc>
    <assembly>
        <name>IT.Microservices.AuthenticationProxy</name>
    </assembly>
    <members>
        <member name="T:IT.Microservices.AuthenticationProxy.ProxyAuth.TestJwtToken">
            <summary>
            Simple test class to verify JWT token parsing functionality
            </summary>
        </member>
        <member name="M:IT.Microservices.AuthenticationProxy.ProxyAuth.TestJwtToken.CreateTestToken(System.String)">
            <summary>
            Creates a simple test JWT token for testing purposes
            </summary>
        </member>
    </members>
</doc>
