﻿namespace ITF.Lib.Common.Availability
{
    public interface IAvailabilityOutput_DeliveryFee
    {
        public string Name { get; set; }
        public Decimal Price { get; set; }
        public Decimal Tax { get; set; }
    }

    public interface IAvailabilityOutput_DeliveryTexts
    {
        public string Name { get; set; }
        public string ShortInfo { get; set; }
        public string LongtInfo { get; set; }
    }

    public interface IAvailabilityOutput_FloristDistance
    {
        public string FloristId { get; set; }
        public double Distance { get; set; }
        public string ShopName { get; set; }
        public string? PhoneNumber { get; set; }
        public double QualityFactor { get; set; }
    }

    public interface IAvailabilityOutput_DeliveryInfo
    {
        public Decimal Price { get; set; }
        public Decimal Compensation { get; set; }
        public bool SelectedByDistributionQuota { get; set; }
        public IAvailabilityOutput_DeliveryTexts Texts { get; set; }
        public List<IAvailabilityOutput_DeliveryFee> Fees { get; set; }
    }

    public interface IAvailabilityOutput
    {
        public bool Result { get; set; }
        public DateTime? SuggestedDeliveryDate { get; set; }
        public virtual IAvailabilityOutput_DeliveryInfo? DeliveryInfo => null;
        public List<IAvailabilityOutput_FloristDistance> FloristDistances { get; set; }
        public string ParishId { get; set; }
    }
}
