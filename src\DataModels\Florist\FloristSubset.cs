﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver.GeoJsonObjectModel;
using System;
using System.Collections.Generic;
using System.Linq;


namespace ITF.SharedModels.DataModels.Florist;

[BsonIgnoreExtraElements]
public class FloristSubset : BaseClass<string>
{
    public DateTime LastSynchronized { get; set; } = DateTime.Now;
    public string FloristId { get; set; } = string.Empty;
    public string PreviousOwner { get; set; } = string.Empty;
    public string Group { get; set; } = string.Empty;
    public string PartnerCode { get; set; } = string.Empty;
    public DateTime InitialisationExecutionDate { get; set; } = DateTime.MinValue;
    public DateTime SuspensionStartDate { get; set; } = DateTime.MinValue;
    public DateTime SuspensionEndDate { get; set; } = DateTime.MinValue;
    public DateTime AgreementStopDate { get; set; } = DateTime.MinValue;
    public DateTime LastModified { get; set; } = DateTime.MinValue;
    public bool IsLocatedInAMall { get; set; } = false;
    public string Name { get; set; } = string.Empty;
    public string OwnerContactName { get; set; } = string.Empty;
    public double QualityBonus { get; set; } = 0.0;
    public DateTime ManualBusinessPerformanceValidTo { get; set; } = DateTime.MinValue;
    public double ManualSalesAmountPerformance { get; set; } = 0.0;
    public double ManualExecutionVolumePerformance { get; set; } = 0.0;
    public double InvolvementFactor { get; set; } = 0.0;
    public double ExecutionSalesAmount { get; set; } = 0.0;
    public double ExecutionVolume { get; set; } = 0.0;
    public double ExecutionTransmissionRatio { get; set; } = 0.0;
    public double TransmissionSalesAmount { get; set; } = 0.0;
    public double TransmissionVolume { get; set; } = 0.0;
    public List<string> Segmentations { get; set; } = new();
    public Address Address { get; set; } = new();
    public List<Contacts> Contacts { get; set; } = new();
    public string APCode { get; set; } = string.Empty;
    public int DailyCap { get; set; } = 0;
    public double Distance { get; set; } = 0.0;
    public List<Calendar> Calendars { get; set; } = new();
    public List<CalendarException> CalendarExceptions { get; set; } = new();
    public List<NonDeliveredPlace> NonDeliveredPlaces { get; set; } = new();
    public List<StockReference> StockReferences { get; set; } = new();
    public List<OrderAssigned> OrdersAssigned { get; set; } = new();
    public List<OrderRejected> OrdersRejected { get; set; } = new();
    public double? StraightLineDistance { get; set; } = null;
    public List<string> OutOfStockFloralReferences { get; set; } = new();
    public List<string> BlockedFloristIdAsTransmitter { get; set; } = new();

    public override void SetId() => Id = FloristId;


}
[BsonIgnoreExtraElements]
public class Address
{
    public string Street { get; set; } = string.Empty;
    public string ZipCode { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public GeoJsonPoint<GeoJson2DGeographicCoordinates> Gps { get; set; } =
        new GeoJsonPoint<GeoJson2DGeographicCoordinates>(
            new GeoJson2DGeographicCoordinates(0.0, 0.0)
        );

    public double GetLatitude => Gps.Coordinates.Latitude;
    public double GetLongitude => Gps.Coordinates.Longitude;

}
[BsonIgnoreExtraElements]
public class Calendar
{
    public string _id { get; set; } = string.Empty;
    public string FloristId { get; set; } = string.Empty;
    public string ClosedWindow { get; set; } = string.Empty;
    public int DayOfWeek { get; set; } = 0;
    public bool DeliveryAvailable { get; set; } = false;
    public int? MaxDistanceOnDeliveryAvailable { get; set; } = null;
    public DateTime? WorkHourClosing { get; set; } = null;
    public DateTime? WorkHourOpening { get; set; } = null;

}
[BsonIgnoreExtraElements]
public class CalendarException
{
    public string _id { get; set; } = string.Empty;
    public DateTime Date { get; set; } = DateTime.MinValue;
    public string Reason { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string Window { get; set; } = string.Empty;
    public DateTime? WorkHourClosing { get; set; } = null;
    public DateTime? WorkHourOpening { get; set; } = null;

}
[BsonIgnoreExtraElements]
public class Contacts
{
    public string PartyNumber { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string Purpose { get; set; } = string.Empty;
    public bool IsPrimary { get; set; } = false;

}
[BsonIgnoreExtraElements]
public class StockReference
{
    public string _id { get; set; } = string.Empty;
    public string ProductId { get; set; } = string.Empty;
    public int Quantity { get; set; } = 0;

}