namespace IT.Microservices.AuthenticationProxy.ProxyAuth.Configuration;

public class TokenIntrospectionConfiguration
{
    public const string SectionName = "TokenIntrospection";
    
    public Dictionary<string, IssuerSettings> Issuers { get; set; } = new();
    
    public TimeoutSettings Timeouts { get; set; } = new();
}

public class IssuerSettings
{
    public string IssuerUrl { get; set; } = string.Empty;
    public string IntrospectionEndpoint { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
}

public class TimeoutSettings
{
    public int IntrospectionTimeoutSeconds { get; set; } = 5;
    public int HttpClientTimeoutSeconds { get; set; } = 30;
}
