﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
	<ImplicitUsings>enable</ImplicitUsings>
	<Nullable>enable</Nullable>
    <Description>
		Interflora Shared Models
		v 8.45.0 : Update ITF.Lib.Common reference to 5.7.0
		v 8.44.1 : Fix Capping Model
		v 8.44.0 : Add Capping
		v 8.43.0 : added size and style check to compute the variantKey in GetVariantKey
		v 8.42.3 : added DeliveryPrice to classes CreateExternalDeliveryMessage, CreateExternalDeliveryPayload
		v 8.42.2 : Init vatNumber, companyNumber and Pec to globalOrder to empty
		v 8.42.1 : Add vatNumber, companyNumber and Pec to globalOrder
		v 8.42.0 : Add RaoOrderModel
		v 8.41.1 : Fix version
		v 8.41.0 : Update ITF.Lib.Common reference to 5.6.0
		v 8.40.0 : Rework mapping between Rao CT lineItem
		v 8.39.0 : Update ITF.Lib.Common reference to 5.5.0
		v 8.38.1 : Fix mapping executingFloristAmount with fee for productSync
		v 8.38.0 : Add OrderCustomerFeedback model
		v 8.37.1 : Fix SalesOrigin order mapping
		v 8.37.0 : Add NonDeliveredPlaces to FloristModel
		v 8.36.0 : Handle SalesOrigin Order Sync
		v 8.35.0 : Update CT lib to latest version for CustomerGroupAssignments fields to be available for group account managment
		v 8.34.0 : Add OrderClaim model
		v 8.33.1 : Rework Billing country Code mapping fr messages
		v 8.33.0 : Rework Bundle Extension Method
		v 8.32.0 : Add composition for GlobalOrderProduct for FR PSM custom description
		v 8.31.0 : Enriched Gfs related product messages
		v 8.30.1 : rework GlobalOrderProduct model
		v 8.30.0 : Change SetId to Id = OrderId for OrderLineItemsPrices object
		v 8.29.0 : Added ContactTitle default to MRS and fix some null/empty to MRS by default too
		v 8.28.0 : Added Gfs related product messages
		v 8.27.6 : Remove useless implicit operator florist message
		v 8.27.5 : Add Enum MassiveCalendarMorningAfternoon
		v 8.27.4 : Rework floristMessage implicit operator with RaoMessage
		v 8.27.3 : Add FloristStockConf Model file
		v 8.27.2 : Add FloristStockConf Model
		v 8.27.1 : Add LegacyOrderNumber in AssignationRemovedMessage
		v 8.27.0 : Add code AP field in GlobalOrderModel
		v 8.26.0 : Added values to AddressTypeEnum
		v 8.25.0 : Rework GetEligibleProducts rule
		v 8.24.0 : improve greetings mapping for CT
		v 8.23.0 : add method for CT comparison
		v 8.22.0 : add productSelection messages for CT
		v 8.21.0 : rework window and moment handling
		v 8.20.0 : rework OrderLineItemsPrices Mongodb model for France Bundled Orders
		v 8.19.0 : Add executingFloristAmount in orderItemupdated Message
		v 8.18.1 : Fix wrong OrderId mapping (OrderNumber instead of CtOrderId) for Legacy Messages
		v 8.18.0 : Fix implicit cast messages without getting the previous MessageId from the initial message
		v 8.17.0 : Fix IMessageKey
		v 8.16.0 : Add extensions method for product sync
		v 8.15.0 : Add OrderLineItemsPrices Mongodb model for France Bundled Orders
		v 8.14.2 : Add count in Florist Kpi model
		v 8.14.1 : Map email florist created message to secondMail
		v 8.14.0 : Change for ExectutingFloristAmount
		v 8.13.14 : Change status mapping for AFF to ACCEPTED (again)
		v 8.13.13 : Revert Change status mapping for AFF to ACCEPTED
		v 8.13.12 : Change status mapping for AFF to ACCEPTED
		v 8.13.11 : Fix shop Name florist sync
		v 8.13.10 : Handle PSM product convert for order sync
		v 8.13.9 : Handle MME Greetings rao messages convert to MRS for CT
		v 8.13.8 : Handle many properties for mapping rao messages
		v 8.13.7 : Handle COLI product Type mapping rao messages
		v 8.13.6 : fix companyName Mapping and executingFloristDeliveryAmount
		v 8.13.5 : fix location can be empty for product model
		v 8.13.4 : fix miscellaneous preprod error : exclude REM to eligibleProducts tested...
		v 8.13.3 : fix miscellaneous preprod error : exclude REM to eligibleProducts
		v 8.13.2  : fix miscellaneous preprod error : handle nullable Size
		v 8.13.1  : fix miscellaneous preprod error : freePrice product
		v 8.13.0  : Updated GlobalOrderExternalDeliveryFieldsUpdated and DeliveryServiceProviderModel
		v 8.12.3  : fix miscellaneous preprod error
		v 8.12.2  : fix miscellaneous preprod error
		v 8.12.1  : fix reverse condition to exclude FDL product
		v 8.12.0  : Add fix for order product according to elastic log result preprod
		v 8.11.0  : Add Florist Kpi model
		v 8.10.0  : Add fix for order product according to elastic log result preprod
		v 8.9.0  : Added ITF.SharedModels.Messages.Group.Order.CreateExternalDeliveryMessage
		v 8.8.0  : OrderAssignmentPayload inheritance basOrderPayload
		v 8.7.0  : Add implicits operators for floristLegacyMessages to handle second FR migration part
		v 8.6.0  : Handle Moment as Enum insteand of string in OrderMessage
		v 8.5.0  : Add converter for OrderPlacedPayload to OrderUpdatedPayload
		v 8.4.1  : Handle more productId in productConverter
		v 8.4.0  : Inhib Product Update CT through Messages
		v 8.3.0  : Add LegacyOrderItemExecutorAmountUpdatedMessage
		v 8.2.0  : Add implicits operators for orderLegacyMessages to handle first FR migration part
		v 8.1.0  : Add GlobalOrderExternalDeliveryFieldsUpdated model
		v 8.0.2  : Fix wrong behavior with model validation after the upgrade - GlobalOrderModel
		v 8.0.1  : Rollback MongoDb Version to V2
		v 8.0.0 : Update to .NET 8
		v 7.76.0 : add LegacyOrderDocumentGeneratedMessage for tracking event of Pentaho generated document to MS
		v 7.75.0 : add EndPoint field in DeliveryServiceProviderModel
		v 7.74.0 : add companyName field in globalOrderModel
		v 7.73.0 : add additionnalStreetInfo, invoiceFirstName and invoiceLastName fields in globalOrderModel
		v 7.72.0 : add resources event for business unit entity type from ct
		v 7.71.0 : modify customer ref type for ressource event ct
		v 7.70.5 : add SecondMail in Florist Contact Enum
		v 7.70.4 : adapt FloristFreshPortalMessage properties to handle nullable fields
		v 7.70.3 : adapt FloristFreshPortalMessage properties to handle nullable fields
		v 7.70.2 : adapt FloristFreshPortalMessage properties
		v 7.70.1 : adapt FloristFreshPortalMessage properties
		v 7.70.0 : add FloristFreshPortalMessage
		v 7.69.0 : add IN_DELIVERY order status enum
		v 7.68.1 : Fix method Equals on GlobalFloristModel
		v 7.68.0 : add IsInvoiceRequested field in globalOrderModel
		v 7.67.0 : update deps
		v 7.66.0 : Add CommerceTools Messages and Payload realated to BusinessUnit entity from CT (for DK B2B needs)
		v 7.65.0 : Add DeliveryServiceProvider model
		v 7.64.0 : Update CT SDK to latest Version / Remove old messages not supported into new CT SDK version
		v 7.63.0 : Add Messages and models for deliveryStatus and deliveryCost
		v 7.62.0 : Add Messages for sync_product topic on CT french migration
		v 7.61.0 : FLR-1375 Handle OrderRecipientCoordinatesUpdated
		v 7.60.1 : Add log error in ErrorObjectResponse
		v 7.60.0 : Add log error in ErrorObjectResponse
		v 7.59.1 : FLR-847 Added field Province to LegacyOrderCreatedPayload.LegacyShipping and ShippingDTO
		v 7.59.0 : FLR-847 Added field Province to ITF.SharedModels.DataModels.Order.GlobalOrderShipping
		v 7.58.0 : Fix confusion between Notes and Comments property
		v 7.57.0 : Add FullCatalogFinishedMessage Message
		v 7.56.0 : Add Category OrderNote property
		v 7.55.0 : Add REFUSED type notification
		v 7.54.0 : Add order note model
		v 7.53.0 : Add order Id on order notification model (used to update notification)
		v 7.52.0 : Add isRead on order notification model
		v 7.51.0 : FLR-1249 Add a new field into the Assignation Order Events to get the product amount for the executor
		v 7.50.0 : FLR-1427 Added TaxCategory to GlobalOrderProduct
		v 7.49.0 : add OrderNotificationDto
		v 7.48.0 : add enum OrderNotificationType
		v 7.47.1 : patch on LegacyOrderAssignedMessage.GetMessageKey()
		v 7.47.0 : added ITF.SharedModels.Messages.Italy.Florist.Legacy.LegacyFloristAttributeUpdatedMessage
		v 7.46.0 : remove password from UserModel
		v 7.45.0 : modified StartDate and EndDate to UserModel to nullable properties
		v 7.44.0 : added Composition into ITF.SharedModels.DataModels.Order.GlobalOrderProduct v2
		v 7.43.0 : added StartDate and EndDate to UserModel and adapat UserPrincipalName into Username
		v 7.42.0 : added ExecutingFloristType into ITF.SharedModels.DataModels.Order.GlobalOrderModel
		v 7.41.0 : upgrade lib.common version
		v 7.40.0 : Added ChangeOrigin to CatalogMessage to track the origin of changes
		v 7.39.0 : Added CatalogMessage for French Product Sync from ERP to CT
		v 7.38.0 : Added Dictionary[string, string] Attributes to GlobalFloristModel
		v 7.37.0 : Added [BsonIgnoreExtraElements] to UserModel
		v 7.36.0 : Refine UserModel and FloristModel to not sustain anymore User table in mongoDB
		v 7.35.0 : Added ITF.SharedModels.DataModels.Globals.ErrorObjectReponse
		v 7.34.0 : Added ITF.SharedModels.Group.Enums.DocTypeEnum
		v 7.33.0 : Add some order fields ans enums
		v 7.32.1 : Fixed GlobalOrderRecipientNameUpdated
		v 7.32.0 : Added GlobalOrderRecipientPhoneNumberUpdated and GlobalOrderRecipientNameUpdated
		v 7.31.0 : Added LegacyOrderRecipientPhoneNumberUpdatedMessage and LegacyOrderRecipientNameUpdatedMessage
		v 7.30.3 : Typo on GlobalOrderModel.DeliveryCountryCode
		v 7.30.2 : Patch on GlobalOrderModel.Equals(GlobalOrderModel parameter)
		v 7.30.1 : Added [BsonIgnoreExtraElements] to GlobalOrderHistoryRecord
		v 7.30.0 : fixed GlobalOrderHistoryRecord.Id generation
		v 7.29.0 : Moved OrderNewHistoryRecordMessageBuilder + added Message field into GlobalOrderHistoryRecord
		v 7.28.0 : Added OrderNumberGeneratedMessage
		v 7.27.0 : Modified DocumentGenerated model by adding OctopusOrderId and CTOrderId
		v 7.26.0 : Added operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderSentMessage v)
		v 7.25.1 : Added Suspended to GlobalFloristModel.FloristActivity
		v 7.25.0 : Added LegacyFloristSuspendedMessage
		v 7.24.0 : Added Florists list to special days remove event
		v 7.23.0 : Update reference MongoDB.Driver to 2.23.1
		v.7.22.0 : Added LegacyOrderSentMessage + ITF.SharedModels.Group.Enums.SENT
		v.7.21.0 : Added GlobalOrderHistoryRecordBuilder into GlobalOrderHistoryRecord.cs
		v.7.20.0 : Added GlobalOrderHistoryRecord + OrderNewHistoryRecordMessage
		v.7.19.0 : Added LegacyOrderAcceptedOnBehalfMessage + LegacyOrderRejectedOnBehalfMessage
		v.7.18.0 : Added ITF.SharedModels.DataModels.Order.GlobalOrderAssignationRemoved
		v.7.17.0 : Added ITF.SharedModels.Messages.Italy.Order.Legacy.LegacyOrderAssignationRemovedMessage
		v.7.16.3 : Added paremeters-less constructor to PfsFloristAccessoryStockUpdatedMessage
		v.7.16.2 : Add some fields to GlobalOrderModel
		v.7.16.1 : Patch on LegacyFloristAddressUpdatedPayload
		v.7.16.0 : Added GlobalOrderItemUpdated and LegacyOrderItemUpdatedMessage, fix LegacyFloristAddressUpdated and lobalFloristModel
		v.7.15.4 : fix: updated GlobalFloristModel.cs
		v.7.15.3 : fix version
		v.7.15.2 : fix version
		v.7.15.1 : Updated GlobalFloristModel class
		v.7.15.0 : Added LegacyFloristAddressUpdatedMessage class
		v.7.14.0 : Added GlobalOrderModel, GlobalOrderAssigned, GlobalOrderCancelled, GlobalOrderDeliveryTimeUpdated
		v.7.13.0 : Addeded cast to messages for GlobalOrderRejected, GlobalOrderDelivered
		v.7.12.0 : Added GlobalOrderAccepted, GlobalOrderRejected, GlobalOrderDelivered + old GlobalOrderDelivered renamed to GlobalOrderDeliveredOnBehalf
		v.7.11.0 : Added LegacyOrderDeliveredMessage, LegacyOrderAcceptedMessage, LegacyOrderRejectedMessage
		v.7.10.0 : Added GlobalOrderCardMessageUpdated, GlobalOrderDelivered, GlobalOrderDeliveryAddressUpdated, GlobalOrderDeliveryDateUpdated, GlobalOrderNotesUpdated
		v.7.9.0 : Added enum for Product/Item menagement
		v.7.8.3 : fix version
		v.7.8.2 : Added [BsonIgnoreExtraElements] to GlobalFloristModel
		v.7.8.1 : Remove GlobalFloristModel subCodes because same property is now in UserModel shopCodes
		v.7.8.0 : Add Amount field in AvailabilityPerformedPayload
		v.7.7.80 : refactor: changed class structure in PfsFloristAccessoryStockUpdatedMessage.cs
		v.7.7.79 : refactor: changed class structure in PfsFloristWeekCalendarUpdatedMessage.cs
		v.7.7.78 : refactor: renamed two properties in PfsFloristWeekCalendarUpdatedMessage.cs
		v.7.7.77 : fix: removed IsClosed from WeekCalendar
		v.7.7.76 : PfsFloristAccessoryStockUpdatedPayload: replaced Quantity with InStock
		v.7.7.75 : feat: Florist Accessory Stock Updated
		v 7.7.74 : added field Province to ITF.SharedModels.DataModels.Florist.ShopLocation
		v 7.7.73 : added field Province to ITF.SharedModels.DataModels.Florist.ShopLocation
		v 7.7.72 : added ITF.SharedModels.Messages.Italy.Order.Legacy.LegacyOrderDeliveredOnBehalfMessage
		v 7.7.71 : fix version number
		v 7.7.70 : fix version number
		v 7.7.69 : add some comments on contact and user fields. Add user dispaly name, and isDeleted on user. Add unknown enum to user roles
		v 7.7.68 : fix on Document
		v 7.7.67 : Update reference
		v 7.7.66 : fix on PersonalCode
		v 7.7.65 : add enum for user roles
		v 7.7.64 : fix some imports
		v 7.7.63 : split GlobalFloristModel to create an other model "User"
		v 7.7.62 : fix: Florists Accessories Added and Removed - Error tags
		v 7.7.61 : feat: Florists Accessories Added and Removed
		v 7.7.60 : feat: added RepositoryNames.OrderHistory
		v 7.7.59 : updates: renamed LegacyFloristDocumentGeneratedMessage.cs as LegacyDocumentGeneratedMessage.cs and modified GlobalFloristModel.cs
		v 7.7.58 : refactor: explicited int values for ITF.SharedModels.Group.Enums.SpecialDayTypeEnum
		v 7.7.57 : feature: added ITF.SharedModels.Messages.Group.Florist.Pfs.PfsFloristSpecialDayAddedMessage and PfsFloristSpecialDayRemovedMessage
		v 7.7.56 : fix: csproj
		v 7.7.55 : fix: adjusted PfsFloristEmailUpdatedMessage and PfsFloristEmailUpdatedMessage
		v 7.7.54 : feature: added ITF.SharedModels.Messages.Group.Florist.Pfs.PfsFloristEmailUpdatedMessage
		v 7.7.53 : feature: added ITF.SharedModels.Messages.Group.Florist.Pfs.PfsFloristPhoneNumberUpdatedMessage
		v 7.7.52 : reafactor: Accessory.Quantity(int) into Accessory.InStok(bool)
		v 7.7.51 : feat: added longitude and latitude to /Messages/Italy/Order/Legacy/LegacyOrderDeliveryAddressUpdatedMessage
		v 7.7.50 : Added longitude and latitude to Messages/Italy/Order/Legacy/Shipping
		v 7.7.49 : Removed deliveryFloristIdnetifier and DeliveryFloristAmount from Order
		v 7.7.48 : Added ToBeAcceptedBefore to LegacyOrderAssignedPayload
		v 7.7.47 : Added SpecialDay cast from LegacySpecialDayAddedMessage and LegacySpecialDayRemovedMessage
		v 7.7.46 : Added LegacyFloristSubcodesAddedMessage and LegacyFloristSubcodesRemovedMessage
		v 7.7.45 : Remvoed unused methods from italy messages
		v 7.7.44 : Added LegacySpecialDayAddedMessage and LegacySpecialDayRemovedMessage
		v 7.7.43 : Added FloristOpenWholeDay to SpecialDayTypeEnum
		v 7.7.42 : CtOrderCustomAttributesNames moved to ITF.SharedLibraries
		v 7.7.41 : Refactor LegacyOrderAssignedMessage, LegacyOrderCanceledMessage, LegacyOrderCardMessageUpdatedMessage, LegacyOrderCreatedMessage, LegacyOrderDeliveryAddressUpdatedMessage, LegacyOrderDeliveryDateUpdatedMessage, LegacyOrderDeliveryTimeUpdatedMessage, LegacyOrderNotesUpdatedMessage
		v 7.7.40 : Refactor LegacyFloristBlockedExecutionMessage, LegacyFloristBlockedTransmissionMessage, LegacyFloristDeletedMessage, LegacyFloristDocumentGenerated
		v 7.7.39 : Refactor LegacyFloristCreatedMessage
		v 7.7.38 : Rename LegacyOrderDeliveryAddressUpdateAddressd to LegacyOrderDeliveryAddressUpdated
		v 7.7.37 : Added LegacyOrderDeliveryAddressUpdateAddressd to Messages/Italy/Order/Legacy
		v 7.7.36 : Updated values for SpecialDayTypeEnum
		v 7.7.35 : Added LegacyOrderNotesUpdatedMessage and LegacyOrderCardMessageUpdatedMessage to Messages/Italy/Order/Legacy
		v 7.7.34 : Added LegacyOrderDeliveryDateUpdatedMessage to Messages/Italy/Order/Legacy
		v 7.7.33 : fix: LegacyOrderDeliveryTimeUpdatedMessage.Time set string
		v 7.7.32 : fix: LegacyOrderDeliveryTimeUpdatedMessage.Time set as nullable
		v 7.7.31 : Added LegacyOrderDeliveryTimeUpdatedMessage to Messages/Italy/Order/Legacy
		v 7.7.30 : Addes classe for legacy document
		v 7.7.29 : Added/renamend field into florist, DailyCalendar, SpecialDay
		v 7.7.28 : Italy: managed LegacyFloristDocumentGeneratedMessage
		v 7.7.27 : Added ITF.SharedModels.Messages.Italy.Order.Legacy.LegacyOrderCancelledMessage.cs
		v 7.7.26 : Added GlobalFloristOrderPerDayModel to florist group model
		v 7.7.25 : Added internalOrderId to ITF.SharedModels.DataModels.Florist.GlobalFloristModel + refactor class, enum names + temporary solution for evaluating internal order id
		v 7.7.24 : Renamed class within ITF.SharedModels.DataModels.Florist.GlobalFloristModel
		v 7.7.23 : Added ITF.SharedModels.DataModels.Florist.GlobalFloristModel
		v 7.7.22 : Italy: renamed classes and fields
		v 7.7.21 : Italy: added credentials and subcodes to KafkaFlorist
		v 7.7.20 : Italy: added marketing fee to KafkaProduct
		v 7.7.19 : Italy domain field within payload classes renamed
		v 7.7.18 : Directories and namespaces renamed
		v 7.7.17 : Removed DTO, Messages and data model moved in the same file, directories and namespaces renamed
		v 7.7.16 : Adapt FloristGroupModel to db projection
		v 7.7.14 : Added OrderAssignedDTO, LegacyOrderAssignedMessage to italy dirs
		v 7.7.13 : Added OrderDTO, LegacyOrderCreatedMessage to italy dirs + enum MomentEnum in the group enum folder
		v 7.7.12 : BlockedInExecution, BlockedInTransmission, Deleted moved from italy Florist to italy FloristActivity
		v 7.7.11 : Minor changes: variable names + enum values
		v 7.7.10 : Added more fiels in Florist group model. Added Document type
		v 7.7.9 : Added ITF.SharedModels.Group.Enums.ContactType
		v 7.7.8 : Added Year and Month fields to the italy document
		v 7.7.7 : Renamed field
		v 7.7.6 : Fix italy model
		v 7.7.5 : Fix italy model
		v 7.7.4 : Fix Florist group model
		v 7.7.3 : Added Florist group model
		v 7.7.2 : Refactor italy classes
		v 7.7.1 : Added Deleted, BlockedExecution, BlockedTransmission to LegacyFlorist (italy)
		v 7.7.0 : Added italy classes for florist deleted, blocked in transmission and in execution
		v 7.6.2 : Added missing florist repository classes
		v 7.6.1 : Added missing florist created message and payload
		v 7.6.0 : Added created florist DTO
		v 7.5.1 : Missing domain event for deleted accessory
		v 7.5.0 : Added add an accessory to florist's stock
		v 7.4.0 : Added florist opening hours
		v 7.3.1 : Added florist country for accessories setup and stock update
		v 7.3.0 : Added florist events, payloads ans messages for contact defined, shop located and personal code defined (AP code)
		v 7.2.0 : Add courrier status fields on Shopopop messages for deliver creation
		v 7.1.0 : Add courrier status fields on Shopopop messages
		v 6.23.2 : Added 1 new Shopopop event message
		v 6.23.1 : Added 1 new Shopopop event message
		v 6.23.0 : Added Shopopop event messages
		v 6.22.0 : Add OrderRejected in florist Payload
		v 6.21.0 : Add florist creation event and notifications
		v 6.20.0 : Add availability performed event
		v 6.19.0 : Downgrade CommerdeTools library
		v 6.18.0 : Split cart into two models
		v 6.17.0 : Update dependencies
		v 6.16.0 : Set GetMessageKey null proof
		v 6.15.0 : Update AbandonedCart payload
		v 6.14.0 : Add AbandonedCart message
		v 6.13.0 : Add florist delivery courier events
		v 6.12.1 : Fix lib version
		v 6.12.0 : Update dependencies
		v 6.11.1 : Fix entity projection in resources payloads for Product, Category and Order
		v 6.11.0 : Add delivery courier events
		v 6.10.0 : Add Payment events from CT
		v 6.9.0 : Update CommerceTools dependencies + fix their namespaces/naming
		v 6.8.1 : Fix some GFS messages types
		v 6.8.0 : Wrap all GFS messages
		v 6.7.0 : Add some fields on existing GFS payloads
		v 6.6.0 : Add all Order/Customer commerces tools event message
		v 6.5.0 : Refactor message events (reference CT objects rather than inherit them) / update dependencies
		v 6.4.0 : Add all Category/Product commerces tools event changes sub delivery events
		v 6.3.0 : Add all Category/Product commerces tools event message
		v 6.2.0 : Add first commerces tools event message
		v 6.1.0 : Update dependencies
		v 6.0.0 : Target .net 6
		v 5.2.0 : Update dependencies
		v 5.1.0 : Implement new interface (remove ExpiryTimeGlobal) / add GetMessageKey for some legacy messages
		v 5.0.0 : Breaking changes : encapsulate notification in static class for versioning
		v 4.6.0 : Add florist sync events
		v 4.5.1 : Fix messageKey for message from gfs sync
		v 4.5.0 : Add gfs sync events
		v 4.4.0 : Add catalog sync events
		v 4.3.3 : modify props of dynamics sent message
		v 4.3.2 : fix cast problem
		v 4.3.1 : Add Message and payload for dynamics sync event
		v 4.3.0 : Remove DDD shared kernel class
		v 4.2.2 : Add baseOrderMessage / payload to factorise
		v 4.2.1 : add missing field AdditionnalTripCost in common delivery
		v 4.2.0 : Add 3 more messages order related
		v 4.1.0 : Add Version for order related payloads
		v 4.0.0 : [BREAKING CHANGE] Use external lib for common interfaces and base class
		v 3.0.0 : [BREAKING CHANGE] Introduce versioning message namespace
		v 2.3.0 : Add fields for OrderAssignmentPayload (AdditionalAddress)
		v 2.2.0 : Change phone fields for OrderAssignmentPayload
		v 2.1.0 : Add fields for OrderAssignmentPayload
		v 2.0.0 : Breaking changes for OrderAssignmentPayload (data compliant with Invoice generation) / Add fields for 2 other payloads
		v 1.1.0 : Add semantic versioning
		v 1.0.17 : Add Synchronization message for florist
		v 1.0.16 : Add interface to provide Kafka key when batched
		v 1.0.15 : Add Synchronization message
		v 1.0.14 : Add AP code support
		v 1.0.13 : Update notification payload
		v 1.0.12 : Update Order assignment payload / set back enum
		v 1.0.11 : Upgrade to Net 5.0
		v 1.0.10 : Refactor notification messages and payloads
		v 1.0.9 : Update RAO legacy models
		v 1.0.8 : Add business models for message handling
		v 1.0.7 : Add correlation logs id in events
		v 1.0.6 : Remove unused class / refactor
		v 1.0.5 : Add BDC enum for OrderMessage
		v 1.0.4 : CreatedAt is initialized
		v 1.0.3 : Add base event class
		v 1.0.2 : Add interfaces specifying if domain event should be propagated on the bus
		v 1.0.1 : Create abstract BaseMessage for message brokers / remove useless domain objects
	</Description>
    <PackageReleaseNotes>
		v 8.45.0 : Update ITF.Lib.Common reference to 5.7.0
		v 8.44.1 : Fix Capping Model
		v 8.44.0 : Add Capping
		v 8.43.0 : added size and style check to compute the variantKey in GetVariantKey
		v 8.42.3 : added DeliveryPrice to classes CreateExternalDeliveryMessage, CreateExternalDeliveryPayload
		v 8.42.2 : Init vatNumber, companyNumber and Pec to globalOrder to empty
		v 8.42.1 : Add vatNumber, companyNumber and Pec to globalOrder
		v 8.42.0 : Add RaoOrderModel
		v 8.41.1 : Fix version
		v 8.41.0 : Update ITF.Lib.Common reference to 5.6.0
		v 8.40.0 : Rework mapping between Rao CT lineItem
		v 8.39.0 : Update ITF.Lib.Common reference to 5.5.0
		v 8.38.1 : Fix mapping executingFloristAmount with fee for productSync
		v 8.38.0 : Add OrderCustomerFeedback model
		v 8.37.1 : Fix SalesOrigin order mapping
		v 8.37.0 : Add NonDeliveredPlaces to FloristModel
		v 8.36.0 : Handle SalesOrigin Order Sync
		v 8.35.0 : Update CT lib to latest version for CustomerGroupAssignments fields to be available for group account managment
		v 8.34.0 : Add OrderClaim model
		v 8.33.1 : Rework Billing country Code mapping fr messages
		v 8.33.0 : Rework Bundle Extension Method
		v 8.32.0 : Add composition for GlobalOrderProduct for FR PSM custom description
		v 8.31.0 : Enriched Gfs related product messages
		v 8.30.1 : rework GlobalOrderProduct model
		v 8.30.0 : Change SetId to Id = OrderId for OrderLineItemsPrices object
		v 8.29.0 : Added ContactTitle default to MRS and fix some null/empty to MRS by default too
		v 8.28.0 : Added Gfs related product messages
		v 8.27.6 : Remove useless implicit operator florist message
		v 8.27.5 : Add Enum MassiveCalendarMorningAfternoon
		v 8.27.4 : Rework floristMessage implicit operator with RaoMessage
		v 8.27.3 : Add FloristStockConf Model file
		v 8.27.2 : Add FloristStockConf Model
		v 8.27.1 : Add LegacyOrderNumber in AssignationRemovedMessage
		v 8.27.0 : Add code AP field in GlobalOrderModel
		v 8.26.0 : Added values to AddressTypeEnum
		v 8.25.0 : Rework GetEligibleProducts rule
		v 8.24.0 : improve greetings mapping for CT
		v 8.23.0 : add method for CT comparison
		v 8.22.0 : add productSelection messages for CT
		v 8.21.0 : rework window and moment handling
		v 8.20.0 : rework OrderLineItemsPrices Mongodb model for France Bundled Orders
		v 8.19.0 : Add executingFloristAmount in orderItemupdated Message
		v 8.18.1 : Fix wrong OrderId mapping (OrderNumber instead of CtOrderId) for Legacy Messages
		v 8.18.0 : Fix implicit cast messages without getting the previous MessageId from the initial message
		v 8.17.0 : Fix IMessageKey
		v 8.16.0 : Add extensions method for product sync
		v 8.15.0 : Add OrderLineItemsPrices Mongodb model for France Bundled Orders
		v 8.14.2 : Add count in Florist Kpi model
		v 8.14.1 : Map email florist created message to secondMail
		v 8.14.0 : Change for ExectutingFloristAmount
		v 8.13.14 : Change status mapping for AFF to ACCEPTED (again)
		v 8.13.13 : Revert Change status mapping for AFF to ACCEPTED
		v 8.13.12 : Change status mapping for AFF to ACCEPTED
		v 8.13.11 : Fix shop Name florist sync
		v 8.13.10 : Handle PSM product convert for order sync
		v 8.13.9 : Handle MME Greetings rao messages convert to MRS for CT
		v 8.13.8 : Handle many properties for mapping rao messages
		v 8.13.7 : Handle COLI product Type mapping rao messages
		v 8.13.6 : fix companyName Mapping and executingFloristDeliveryAmount
		v 8.13.5 : fix location can be empty for product model
		v 8.13.4 : fix miscellaneous preprod error : exclude REM to eligibleProducts tested...
		v 8.13.3 : fix miscellaneous preprod error : exclude REM to eligibleProducts
		v 8.13.2  : fix miscellaneous preprod error : handle nullable Size
		v 8.13.1  : fix miscellaneous preprod error : freePrice product
		v 8.13.0  : Updated GlobalOrderExternalDeliveryFieldsUpdated and DeliveryServiceProviderModel
		v 8.12.3  : fix miscellaneous preprod error
		v 8.12.2  : fix miscellaneous preprod error
		v 8.12.1  : fix reverse condition to exclude FDL product
		v 8.12.0  : Add fix for order product according to elastic log result preprod
		v 8.11.0  : Add Florist Kpi model
		v 8.10.0  : Add fix for order product according to elastic log result preprod
		v 8.9.0  : Added ITF.SharedModels.Messages.Group.Order.CreateExternalDeliveryMessage
		v 8.8.0  : OrderAssignmentPayload inheritance basOrderPayload
		v 8.7.0  : Add implicits operators for floristLegacyMessages to handle second FR migration part
		v 8.6.0  : Handle Moment as Enum insteand of string in OrderMessage
		v 8.5.0  : Add converter for OrderPlacedPayload to OrderUpdatedPayload
		v 8.4.1  : Handle more productId in productConverter
		v 8.4.0  : Inhib Product Update CT through Messages
		v 8.3.0  : Add LegacyOrderItemExecutorAmountUpdatedMessage
		v 8.2.0  : Add implicits operators for orderLegacyMessages to handle first FR migration part
		v 8.1.0  : Add GlobalOrderExternalDeliveryFieldsUpdated model
		v 8.0.2  : Fix wrong behavior with model validation after the upgrade - GlobalOrderModel
		v 8.0.1  : Rollback MongoDb Version to V2
		v 8.0.0 : Update to .NET 8
		v 7.76.0 : add LegacyOrderDocumentGeneratedMessage for tracking event of Pentaho generated document to MS
		v 7.75.0 : add EndPoint field in DeliveryServiceProviderModel
		v 7.74.0 : add companyName field in globalOrderModel
		v 7.73.0 : add additionnalStreetInfo, invoiceFirstName and invoiceLastName fields in globalOrderModel
		v 7.72.0 : add resources event for business unit entity type from ct
		v 7.71.0 : modify customer ref type for ressource event ct
		v 7.70.5 : add SecondMail in Florist Contact Enum
		v 7.70.4 : adapt FloristFreshPortalMessage properties to handle nullable fields
		v 7.70.3 : adapt FloristFreshPortalMessage properties to handle nullable fields
		v 7.70.2 : adapt FloristFreshPortalMessage properties
		v 7.70.1 : adapt FloristFreshPortalMessage properties
		v 7.70.0 : add FloristFreshPortalMessage
		v 7.69.0 : add IN_DELIVERY order status enum
		v 7.68.1 : Fix method Equals on GlobalFloristModel
		v 7.68.0 : add IsInvoiceRequested field in globalOrderModel
		v 7.67.0 : update deps
		v 7.66.0 : Add CommerceTools Messages and Payload realated to BusinessUnit entity from CT (for DK B2B needs)
		v 7.65.0 : Add DeliveryServiceProvider model
		v 7.64.0 : Update CT SDK to latest Version / Remove old messages not supported into new CT SDK version
		v 7.63.0 : Add Messages and models for deliveryStatus and deliveryCost
		v 7.62.0 : Add Messages for sync_product topic on CT french migration
		v 7.61.0 : FLR-1375 Handle OrderRecipientCoordinatesUpdated
		v 7.60.1 : Add log error in ErrorObjectResponse
		v 7.60.0 : Add log error in ErrorObjectResponse
		v 7.59.1 : FLR-847 Added field Province to LegacyOrderCreatedPayload.LegacyShipping and ShippingDTO
		v 7.59.0 : FLR-847 Added field Province to ITF.SharedModels.DataModels.Order.GlobalOrderShipping
		v 7.58.0 : Fix confusion between Notes and Comments property
		v 7.57.0 : Add FullCatalogFinishedMessage Message
		v 7.56.0 : Add Category OrderNote property
		v 7.55.0 : Add REFUSED type notification
		v 7.54.0 : Add order note model
		v 7.53.0 : Add order Id on order notification model (used to update notification)
		v 7.52.0 : Add isRead on order notification model
		v 7.51.0 : FLR-1249 Add a new field into the Assignation Order Events to get the product amount for the executor
		v 7.50.0 : FLR-1427 Added TaxCategory to GlobalOrderProduct
		v 7.49.0 : add OrderNotificationDto
		v 7.48.0 : add enum OrderNotificationType
		v 7.47.1 : patch on LegacyOrderAssignedMessage.GetMessageKey()
		v 7.47.0 : added ITF.SharedModels.Messages.Italy.Florist.Legacy.LegacyFloristAttributeUpdatedMessage
		v 7.46.0 : remove password from UserModel
		v 7.45.0 : modified StartDate and EndDate to UserModel to nullable properties
		v 7.44.0 : added Composition into ITF.SharedModels.DataModels.Order.GlobalOrderProduct v2
		v 7.43.0 : added StartDate and EndDate to UserModel and adapat UserPrincipalName into Username
		v 7.42.0 : added ExecutingFloristType into ITF.SharedModels.DataModels.Order.GlobalOrderModel
		v 7.41.0 : upgrade lib.common version
		v 7.40.0 : Added ChangeOrigin to CatalogMessage to track the origin of changes
		v 7.39.0 : Added CatalogMessage for French Product Sync from ERP to CT
		v 7.38.0 : Added Dictionary[string, string] Attributes to GlobalFloristModel
		v 7.37.0 : Added [BsonIgnoreExtraElements] to UserModel
		v 7.36.0 : Refine UserModel and FloristModel to not sustain anymore User table in mongoDB
		v 7.35.0 : Added ITF.SharedModels.DataModels.Globals.ErrorObjectReponse
		v 7.34.0 : Added ITF.SharedModels.Group.Enums.DocTypeEnum
		v 7.33.0 : Add some order fields ans enums
		v 7.32.1 : Fixed GlobalOrderRecipientNameUpdated
		v 7.32.0 : Added GlobalOrderRecipientPhoneNumberUpdated and GlobalOrderRecipientNameUpdated
		v 7.31.0 : Added LegacyOrderRecipientPhoneNumberUpdatedMessage and LegacyOrderRecipientNameUpdatedMessage
		v 7.30.3 : Typo on GlobalOrderModel.DeliveryCountryCode
		v 7.30.2 : Patch on GlobalOrderModel.Equals(GlobalOrderModel parameter)
		v 7.30.1 : Added [BsonIgnoreExtraElements] to GlobalOrderHistoryRecord
		v 7.30.0 : fixed GlobalOrderHistoryRecord.Id generation
		v 7.29.0 : Moved OrderNewHistoryRecordMessageBuilder + added Message field into GlobalOrderHistoryRecord
		v 7.28.0 : Added OrderNumberGeneratedMessage
		v 7.27.0 : Modified DocumentGenerated model by adding OctopusOrderId and CTOrderId
		v 7.26.0 : Added operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderSentMessage v)
		v 7.25.1 : Added Suspended to GlobalFloristModel.FloristActivity
		v 7.25.0 : Added LegacyFloristSuspendedMessage
		v 7.24.0 : Added Florists list to special days remove event
		v 7.23.0 : Update reference MongoDB.Driver to 2.23.1
		v.7.22.0 : Added LegacyOrderSentMessage + ITF.SharedModels.Group.Enums.SENT
		v.7.21.0 : Added GlobalOrderHistoryRecordBuilder into GlobalOrderHistoryRecord.cs
		v.7.20.0 : Added GlobalOrderHistoryRecord + OrderNewHistoryRecordMessage
		v.7.19.0 : Added LegacyOrderAcceptedOnBehalfMessage + LegacyOrderRejectedOnBehalfMessage
		v.7.18.0 : Added ITF.SharedModels.DataModels.Order.GlobalOrderAssignationRemoved
		v.7.17.0 : Added ITF.SharedModels.Messages.Italy.Order.Legacy.LegacyOrderAssignationRemovedMessage
		v.7.16.3 : Added paremeters-less constructor to PfsFloristAccessoryStockUpdatedMessage
		v.7.16.2 : Add some fields to GlobalOrderModel
		v.7.16.1 : Patch on LegacyFloristAddressUpdatedPayload
		v.7.16.0 : Added GlobalOrderItemUpdated and LegacyOrderItemUpdatedMessage, fix LegacyFloristAddressUpdated and lobalFloristModel
		v.7.15.4 : fix: updated GlobalFloristModel.cs
		v.7.15.3 : fix version
		v.7.15.2 : fix version
		v.7.15.1 : Updated GlobalFloristModel class
		v.7.15.0 : Added LegacyFloristAddressUpdatedMessage class
		v.7.14.0 : Added GlobalOrderModel, GlobalOrderAssigned, GlobalOrderCancelled, GlobalOrderDeliveryTimeUpdated
		v.7.13.0 : Addeded cast to messages for GlobalOrderRejected, GlobalOrderDelivered
		v.7.12.0 : Added GlobalOrderAccepted, GlobalOrderRejected, GlobalOrderDelivered + old GlobalOrderDelivered renamed to GlobalOrderDeliveredOnBehalf
		v.7.11.0 : Added LegacyOrderDeliveredMessage, LegacyOrderAcceptedMessage, LegacyOrderRejectedMessage
		v.7.10.0 : Added GlobalOrderCardMessageUpdated, GlobalOrderDelivered, GlobalOrderDeliveryAddressUpdated, GlobalOrderDeliveryDateUpdated, GlobalOrderNotesUpdated
		v.7.9.0 : Added enum for Product/Item menagement
		v.7.8.3 : fix version
		v.7.8.2 : Added [BsonIgnoreExtraElements] to GlobalFloristModel
		v.7.8.1 : Remove GlobalFloristModel subCodes because same property is now in UserModel shopCodes
		v.7.8.0 : Add Amount field in AvailabilityPerformedPayload
		v.7.7.80 : refactor: changed class structure in PfsFloristAccessoryStockUpdatedMessage.cs
		v.7.7.79 : refactor: changed class structure in PfsFloristWeekCalendarUpdatedMessage.cs
		v.7.7.78 : refactor: renamed two properties in PfsFloristWeekCalendarUpdatedMessage.cs
		v.7.7.77 : fix: removed IsClosed from WeekCalendar
		v.7.7.76 : PfsFloristAccessoryStockUpdatedPayload: replaced Quantity with InStock
		v.7.7.75 : feat: Florist Accessory Stock Updated
		v 7.7.74 : added field Province to ITF.SharedModels.DataModels.Florist.ShopLocation 2
		v 7.7.73 : added field Province to ITF.SharedModels.DataModels.Florist.ShopLocation
		v 7.7.72 : added ITF.SharedModels.Messages.Italy.Order.Legacy.LegacyOrderDeliveredOnBehalfMessage
		v 7.7.71 : fix version number
		v 7.7.70 : fix version number
		v 7.7.69 : add some comments on contact and user fields. Add user dispaly name, and isDeleted on user. Add unknown enum to user roles
		v 7.7.68 : fix on Document
		v 7.7.67 : Update reference
		v 7.7.66 : fix on PersonalCode
		v 7.7.65 : add enum for user roles
		v 7.7.64 : fix some imports
		v 7.7.63 : split GlobalFloristModel to create an other model "User"
		v 7.7.62 : fix: Florists Accessories Added and Removed - Error tags
		v 7.7.61 : feat: Florists Accessories Added and Removed
		v 7.7.60 : feat: added RepositoryNames.OrderHistory
		v 7.7.59 : updates: renamed LegacyFloristDocumentGeneratedMessage.cs as LegacyDocumentGeneratedMessage.cs and modified GlobalFloristModel.cs
		v 7.7.58 : refactor: explicited int values for ITF.SharedModels.Group.Enums.SpecialDayTypeEnum
		v 7.7.57 : feature: added ITF.SharedModels.Messages.Group.Florist.Pfs.PfsFloristSpecialDayAddedMessage and PfsFloristSpecialDayRemovedMessage
		v 7.7.56 : fix: csproj
		v 7.7.55 : fix: adjusted PfsFloristEmailUpdatedMessage and PfsFloristEmailUpdatedMessage
		v 7.7.54 : feature: added ITF.SharedModels.Messages.Group.Florist.Pfs.PfsFloristEmailUpdatedMessage
		v 7.7.53 : feature: added ITF.SharedModels.Messages.Group.Florist.Pfs.PfsFloristPhoneNumberUpdatedMessage
		v 7.7.52 : reafactor: Accessory.Quantity(int) into Accessory.InStok(bool)
		v 7.7.51 : feat: added longitude and latitude to /Messages/Italy/Order/Legacy/LegacyOrderDeliveryAddressUpdatedMessage
		v 7.7.50 : Added longitude and latitude to Messages/Italy/Order/Legacy/Shipping
		v 7.7.49 : Removed deliveryFloristIdnetifier and DeliveryFloristAmount from Order
		v 7.7.48 : Added ToBeAcceptedBefore to LegacyOrderAssignedPayload
		v 7.7.47 : Added SpecialDay cast from LegacySpecialDayAddedMessage and LegacySpecialDayRemovedMessage
		v 7.7.46 : Added LegacyFloristSubcodesAddedMessage and LegacyFloristSubcodesRemovedMessage
		v 7.7.45 : Remvoed unused methods from italy messages
		v 7.7.44 : Added LegacySpecialDayAddedMessage and LegacySpecialDayRemovedMessage
		v 7.7.43 : Added FloristOpenWholeDay to SpecialDayTypeEnum
		v 7.7.42 : CtOrderCustomAttributesNames moved to ITF.SharedLibraries
		v 7.7.41 : Refactor LegacyOrderAssignedMessage, LegacyOrderCanceledMessage, LegacyOrderCardMessageUpdatedMessage, LegacyOrderCreatedMessage, LegacyOrderDeliveryAddressUpdatedMessage, LegacyOrderDeliveryDateUpdatedMessage, LegacyOrderDeliveryTimeUpdatedMessage, LegacyOrderNotesUpdatedMessage
		v 7.7.40 : Refactor LegacyFloristBlockedExecutionMessage, LegacyFloristBlockedTransmissionMessage, LegacyFloristDeletedMessage, LegacyFloristDocumentGenerated
		v 7.7.39 : Refactor LegacyFloristCreatedMessage
		v 7.7.38 : Rename LegacyOrderDeliveryAddressUpdateAddressd to LegacyOrderDeliveryAddressUpdated
		v 7.7.37 : Added LegacyOrderDeliveryAddressUpdateAddressd to Messages/Italy/Order/Legacy
		v 7.7.36 : Updated values for SpecialDayTypeEnum
		v 7.7.35 : Added LegacyOrderNotesUpdatedMessage and LegacyOrderCardMessageUpdatedMessage to Messages/Italy/Order/Legacy
		v 7.7.34 : Added LegacyOrderDeliveryDateUpdatedMessage to Messages/Italy/Order/Legacy
		v 7.7.33 : fix: LegacyOrderDeliveryTimeUpdatedMessage.Time set string
		v 7.7.32 : fix: LegacyOrderDeliveryTimeUpdatedMessage.Time set as nullable
		v 7.7.31 : Added LegacyOrderDeliveryTimeUpdatedMessage to Messages/Italy/Order/Legacy
		v 7.7.30 : Addes classe for legacy document
		v 7.7.29 : Added/renamend field into florist, DailyCalendar, SpecialDay
		v 7.7.28 : Italy: managed LegacyFloristDocumentGeneratedMessage
		v 7.7.27 : Added ITF.SharedModels.Messages.Italy.Order.Legacy.LegacyOrderCancelledMessage.cs
		v 7.7.26 : Added GlobalFloristOrderPerDayModel to florist group model
		v 7.7.25 : Added internalOrderId to ITF.SharedModels.DataModels.Florist.GlobalFloristModel + refactor class, enum names + temporary solution for evaluating internal order id
		v 7.7.24 : Renamed class within ITF.SharedModels.DataModels.Florist.GlobalFloristModel
		v 7.7.23 : Added ITF.SharedModels.DataModels.Florist.GlobalFloristModel
		v 7.7.22 : Italy: renamed classes and fields
		v 7.7.21 : Italy: added credentials and subcodes to KafkaFlorist
		v 7.7.20 : Italy: added marketing fee to KafkaProduct
		v 7.7.19 : Italy domain field within payload classes renamed
		v 7.7.18 : Directories and namespaces renamed
		v 7.7.17 : Removed DTO, Messages and data model moved in the same file, directories and namespaces renamed
		v 7.7.14 : Added OrderAssignedDTO, LegacyOrderAssignedMessage to italy dirs
		v 7.7.13 : Added OrderDTO, LegacyOrderCreatedMessage to italy dirs + enum MomentEnum in the group enum folder
		v 7.7.12 : BlockedInExecution, BlockedInTransmission, Deleted moved from italy Florist to italy FloristActivity
		v 7.7.11 : Minor changes: variable names + enum values
		v 7.7.10 : Added more fiels in Florist group model. Added Document type
		v 7.7.9 : Added ITF.SharedModels.Group.Enums.ContactType
		v 7.7.8 : Added Year and Month fields to the italy document
		v 7.7.7 : Renamed field
		v 7.7.6 : Fix italy model
		v 7.7.5 : Fix italy model
		v 7.7.4 : Fix Florist group model
		v 7.7.2 : Refactor italy classes
		v 7.7.1 : Added Deleted, BlockedExecution, BlockedTransmission to LegacyFlorist (italy)
		v 7.7.0 : Added italy classes for florist deleted, blocked in transmission and in execution
		v 7.6.2 : Added missing florist repository classes
		v 7.6.1 : Added missing florist created message and payload
		v 7.6.0 : Added created florist DTO
		v 7.5.1 : Missing domain event for deleted accessory
		v 7.5.0 : Added add an accessory to florist's stock
		v 7.4.0 : Added florist opening hours
		v 7.3.1 : Added florist country for accessories setup and stock update
		v 7.3.0 : Added florist events, payloads ans messages for contact defined, shop located and personal code defined (AP code)
		v 7.2.0 : Add fields in for created deliveries
		v 7.1.0 : Add courrier status fields on Shopopop messages
		v 6.22.0 : Add OrderRejected in florist Payload
		v 6.21.0 : Add florist creation event and notifications
		v 6.20.0 : Add availability performed event
		v 6.19.0 : Downgrade CommerdeTools library
		v 6.18.0 : Split cart into two models
		v 6.17.0 : Update dependencies
		v 6.16.0 : Set GetMessageKey null proof
		v 6.15.0 : Update AbandonedCart payload
		v 6.14.0 : Add AbandonedCart message
		v 6.13.0 : Add florist delivery courier events
		v 6.12.1 : Fix lib version
		v 6.14.0 : Add AbandonedCart message
		v 6.13.0 : Add florist delivery courier events
		v 6.12.1 : Fix lib version
		v 6.12.0 : Update dependencies
		v 6.11.1 : Fix entity projection in resources payloads for Product, Category and Order
		v 6.11.0 : Add delivery courier events
		v 6.10.0 : Add Payment events from CT
		v 6.9.0 : Update CommerceTools dependencies + fix their namespaces/naming
		v 6.8.1 : Fix some GFS messages types
		v 6.8.0 : Wrap all GFS messages
		v 6.7.0 : Add some fields on existing GFS payloads
		v 6.6.0 : Add all Order/Customer commerces tools event message
		v 6.5.0 : Refactor message events (reference CT objects rather than inherit them) / update dependencies
		v 6.4.0 : Add all Category/Product commerces tools event message
		v 6.3.0 : Add all Category/Product commerces tools event message
		v 6.2.0 : Add first commerces tools event message
		v 6.1.0 : Update dependencies
		v 6.0.0 : Target .net 6
		v 5.2.0 : Update dependencies
		v 5.1.0 : Implement new interface (remove ExpiryTimeGlobal) / add GetMessageKey for some legacy messages
		v 5.0.0 : Breaking changes : encapsulate notification in static class for versioning
		v 4.6.0 : Add florist sync events
		v 4.5.1 : Fix messageKey for message from gfs sync
		v 4.5.0 : Add gfs sync events
		v 4.4.0 : Add catalog sync events
		v 4.3.3 : modify props of dynamics sent message
		v 4.3.2 : fix cast problem
		v 4.3.1 : Add Message and payload for dynamics sync event
		v 4.3.0 : Remove DDD shared kernel class
		v 4.2.2 : Add baseOrderMessage / payload to factorise
		v 4.2.1 : add missing field AdditionnalTripCost in common delivery
		v 4.2.0 : Add 3 more messages order related
		v 4.1.0 : Add Version for order related payloads
		v 4.0.0 : [BREAKING CHANGE] Use external lib for common interfaces and base class
		v 3.0.0 : [BREAKING CHANGE] Introduce versioning message namespace
		v 2.3.0 : Add fields for OrderAssignmentPayload (AdditionalAddress)
		v 2.2.0 : Change phone fields for OrderAssignmentPayload
		v 2.1.0 : Add fields for OrderAssignmentPayload
		v 2.0.0 : Breaking changes for OrderAssignmentPayload (data compliant with Invoice generation) / Add fields for 2 other payloads
		v 1.1.0 : Add semantic versioning
		v 1.0.17 : Add Synchronization message for florist
		v 1.0.16 : Add interface to provide Kafka key when batched
		v 1.0.15 : Add Synchronization message
		v 1.0.14 : Add AP code support
		v 1.0.13 : Update notification payload
		v 1.0.12 : Update Order assignment payload / set back enum
		v 1.0.11 : Upgrade to Net 5.0
		v 1.0.10 : Refactor notification messages and payloads
		v 1.0.9 : Update RAO legacy models
		v 1.0.8 : Add business models for message handling
		v 1.0.7 : Add correlation logs id in events
		v 1.0.6 : Remove unused class / refactor
		v 1.0.5 : Add BDC enum for OrderMessage
		v 1.0.4 : CreatedAt is initialized
		v 1.0.3 : Add base event class
		v 1.0.2 : Add interfaces specifying if domain event should be propagated on the bus
		v 1.0.1 : Create abstract BaseMessage for message brokers / remove useless domain objects
	</PackageReleaseNotes>
    <PackageTags>Git on Azure Devops</PackageTags>
    <RepositoryUrl>https://interflorad365fo.visualstudio.com/ITF.Microservices.Order.Domain/_git/ITF.SharedModels</RepositoryUrl>
    <Copyright>Interflora France</Copyright>
    <Version>8.45.0</Version>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="commercetools.Sdk.Api" Version="12.13.0" />
    <PackageReference Include="ITF.Lib.Common" Version="5.7.0" />
    <PackageReference Include="MongoDB.Driver" Version="2.30.0" />
	<PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\ITF.Lib.Common\src\ITF.Lib.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Group\Models\" />
    <Folder Include="Messages\Repo\" />
    <Folder Include="Notifications\Business\CommerceTools\Messages\Payment\" />
    <Folder Include="Notifications\Business\CommerceTools\Payloads\Payment\" />
  </ItemGroup>

</Project>
