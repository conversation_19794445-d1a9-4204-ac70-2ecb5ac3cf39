# Token Introspection Implementation

This implementation provides JWT token introspection functionality similar to the PHP proxy-introspection.php application, but using .NET patterns and the existing shared libraries.

## Overview

The implementation consists of:

1. **ProxyAuthController** - HTTP endpoint that accepts Bearer tokens and returns introspection results
2. **ProxyAuthUseCase** - Main business logic orchestrator
3. **JwtTokenService** - JWT parsing and issuer extraction
4. **IssuerConfigurationService** - Environment-based issuer configuration loading
5. **TokenIntrospectionService** - HTTP client for calling Keycloak introspection endpoints

## API Endpoint

**POST** `/itauthenticationproxy/api/v1/ProxyAuth`

### Request
- **Headers**: `Authorization: Bearer <jwt_token>`
- **Body**: None

### Response
```json
{
  "active": true,
  "sub": "user-id",
  "iss": "https://keycloak.example.com/realms/myrealm",
  "exp": 1640995200,
  "iat": 1640991600,
  "client_id": "my-client",
  "username": "john.doe",
  "scope": "openid profile"
}
```

### Error Response
```json
{
  "active": false,
  "error": "Unknown issuer"
}
```

## Configuration

The application uses `appsettings.json` and `appsettings.Development.json` for configuration with `IOptionsMonitor<TokenIntrospectionConfiguration>` for hot-reload support.

### Configuration Structure

```json
{
  "TokenIntrospection": {
    "Timeouts": {
      "IntrospectionTimeoutSeconds": 5,
      "HttpClientTimeoutSeconds": 30
    },
    "Issuers": {
      "keycloak-dev": {
        "IssuerUrl": "https://keycloak-dev.example.com/realms/development",
        "IntrospectionEndpoint": "https://keycloak-dev.example.com/realms/development/protocol/openid-connect/token/introspect",
        "ClientId": "introspection-client-dev",
        "ClientSecret": "dev-client-secret"
      },
      "keycloak-prod": {
        "IssuerUrl": "https://keycloak.example.com/realms/production",
        "IntrospectionEndpoint": "https://keycloak.example.com/realms/production/protocol/openid-connect/token/introspect",
        "ClientId": "introspection-client",
        "ClientSecret": "your-production-secret"
      }
    }
  }
}
```

### Configuration Classes

- **TokenIntrospectionConfiguration**: Main configuration class
- **IssuerSettings**: Individual issuer configuration
- **TimeoutSettings**: HTTP timeout configuration

The configuration supports hot-reload through `IOptionsMonitor`, so changes to appsettings files are automatically picked up without restarting the application.

## Flow

1. **Token Extraction**: Extract Bearer token from Authorization header
2. **JWT Parsing**: Parse JWT to extract the `iss` (issuer) claim
3. **Issuer Lookup**: Find configuration for the issuer from environment variables
4. **Introspection Call**: Make HTTP POST to the issuer's introspection endpoint with Basic auth
5. **Response**: Return the introspection result

## Error Handling

The implementation handles various error scenarios:

- **400 Bad Request**: Invalid JWT format, missing issuer claim
- **401 Unauthorized**: Missing/invalid Authorization header, unknown issuer
- **500 Internal Server Error**: No issuers configured, HTTP errors, timeouts

## Key Features

- **Environment-based Configuration**: Supports multiple issuers via environment variables
- **JWT Parsing**: Base64 URL-safe decoding and JSON parsing
- **HTTP Client**: Configurable timeout (5 seconds), Basic authentication
- **Structured Logging**: Comprehensive logging with context information
- **Error Handling**: Proper error codes and messages
- **Performance Monitoring**: Request timing and metrics

## Testing

To test the implementation:

1. Set up environment variables for your Keycloak instance
2. Start the application: `dotnet run`
3. Make a POST request with a Bearer token:

```bash
curl -X POST "http://localhost:5000/itauthenticationproxy/api/v1/ProxyAuth" \
  -H "Authorization: Bearer <your-jwt-token>"
```

## Differences from PHP Implementation

While maintaining the same API contract and behavior, this .NET implementation:

- Uses dependency injection for better testability
- Leverages the existing shared libraries for HTTP clients and logging
- Uses structured logging instead of simple error_log
- Implements proper async/await patterns
- Uses the Result pattern for error handling
- Follows .NET naming conventions and patterns
- Uses `appsettings.json` configuration instead of environment variables
- Supports configuration hot-reload through `IOptionsMonitor`
- Provides strongly-typed configuration classes

## Dependencies

- **ITF.SharedLibraries**: HTTP client, logging, configuration
- **CSharpFunctionalExtensions**: Result pattern
- **System.Text.Json**: JSON parsing
- **Microsoft.Extensions.Http**: HTTP client factory
