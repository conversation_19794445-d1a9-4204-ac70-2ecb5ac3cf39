using System.Text.RegularExpressions;

namespace IT.Microservices.AuthenticationProxy.ProxyAuth;

public class ProxyAuthUseCase(
    ILogger<ProxyAuthUseCase> logger,
    IIssuerConfigurationService issuerConfigService,
    IJwtTokenService jwtTokenService,
    ITokenIntrospectionService introspectionService) : IProxyAuthUseCase
{
    public async Task<Result<TokenIntrospectionResponse, Error>> ProcessAsync(string authorizationHeader)
    {
        try
        {
            // Extract token from Authorization header
            var tokenResult = ExtractBearerToken(authorizationHeader);
            if (tokenResult.IsFailure)
            {
                logger.LogError("Failed to extract bearer token: {Error}", tokenResult.Error.Message);
                return tokenResult.Error;
            }

            var token = tokenResult.Value;

            // Parse JWT to extract issuer
            var issuerResult = jwtTokenService.ExtractIssuer(token);
            if (issuerResult.IsFailure)
            {
                logger.LogError("Failed to extract issuer from token: {Error}", issuerResult.Error.Message);
                return issuerResult.Error;
            }

            var issuer = issuerResult.Value;
            logger.LogInformation("Token received (length: {Length}) with issuer: {Issuer}", token.Length, issuer);

            // Get issuer configuration
            var configResult = await issuerConfigService.GetIssuerConfigurationAsync(issuer);
            if (configResult.IsFailure)
            {
                logger.LogError("Failed to get issuer configuration for {Issuer}: {Error}", issuer, configResult.Error.Message);
                return configResult.Error;
            }

            var config = configResult.Value;

            // Call introspection endpoint
            var introspectionResult = await introspectionService.IntrospectTokenAsync(token, config);
            if (introspectionResult.IsFailure)
            {
                logger.LogError("Token introspection failed for issuer {Issuer}: {Error}", issuer, introspectionResult.Error.Message);
                return new TokenIntrospectionResponse { Active = false, Error = introspectionResult.Error.Message };
            }

            var response = introspectionResult.Value;
            logger.LogInformation("Token introspection completed for issuer {Issuer}, active: {Active}", issuer, response.Active);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during token introspection");
            return new Error("INTERNAL_ERROR", "An unexpected error occurred");
        }
    }

    private static Result<string, Error> ExtractBearerToken(string authorizationHeader)
    {
        if (string.IsNullOrWhiteSpace(authorizationHeader))
        {
            return new Error("MISSING_AUTH_HEADER", "Missing or invalid Authorization header");
        }

        var match = Regex.Match(authorizationHeader, @"Bearer\s+(\S+)", RegexOptions.IgnoreCase);
        if (!match.Success)
        {
            return new Error("INVALID_AUTH_HEADER", "Missing or invalid Authorization header");
        }

        return match.Groups[1].Value;
    }
}

public interface IProxyAuthUseCase
{
    Task<Result<TokenIntrospectionResponse, Error>> ProcessAsync(string authorizationHeader);
}