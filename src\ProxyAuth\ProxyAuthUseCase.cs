namespace IT.Microservices.AuthenticationProxy.ProxyAuth;

public class ProxyAuthUseCase(ILogger<ProxyAuthUseCase> logger) : IProxyAuthUseCase
{
    public Result<bool, Error> Process(ProxyAuthInstropectionRequest req)
    {
        return Result.Failure<bool,Error>(Error.None); // implement you logic here
    }
}

public interface IProxyAuthUseCase
{
    public Result<bool, Error> Process(ProxyAuthInstropectionRequest req);
}