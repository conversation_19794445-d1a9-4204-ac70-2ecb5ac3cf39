{"ElasticApm": {"ServerUrl": "http://apm:8200"}, "ElasticSearchLog": {"ElasticSearchLog": "http://elasticsearch:9200/"}, "Unleash": {"Url": "http://unleash:4242/api/"}, "FeatureFlags": {"Provider": "featuremanager"}, "TokenIntrospection": {"Timeouts": {"IntrospectionTimeoutSeconds": 5, "HttpClientTimeoutSeconds": 30}, "Issuers": {"keycloak-dev": {"IssuerUrl": "https://keycloak-dev.example.com/realms/development", "IntrospectionEndpoint": "https://keycloak-dev.example.com/realms/development/protocol/openid-connect/token/introspect", "ClientId": "introspection-client-dev", "ClientSecret": "dev-client-secret"}, "keycloak-test": {"IssuerUrl": "https://keycloak-test.example.com/realms/test", "IntrospectionEndpoint": "https://keycloak-test.example.com/realms/test/protocol/openid-connect/token/introspect", "ClientId": "introspection-client-test", "ClientSecret": "test-client-secret"}}}}