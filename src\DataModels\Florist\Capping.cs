﻿using ITF.Lib.Common.DomainDrivenDesign;
using Microsoft.VisualBasic;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.DataModels.Florist;

[BsonIgnoreExtraElements]
public class Capping : BaseClass<string>
{
    public Information Name { get; set; }
    public DateTime StartDisplayDate { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public DateTime? LastModified { get; set; }
    public User CreatedBy { get; set; }
    public User ModifiedBy { get; set; }

    /*
        * KNOWN RULES
        * ___________
        * starting date (with time part) and end date are unique
        * FloristSettingss can be archived (deleted + history stored)
       * QUERIES
       * ________
       * Get all FloristSettingss
       * Get the active FloristSettings
       */

    protected Capping()
    {
    }

    public Capping(Information name, DateTime startDisplayDate, DateTime startDate, DateTime endDate, User createdBy)
    {
        Name = name;
        StartDisplayDate = startDisplayDate;
        StartDate = startDate;
        EndDate = endDate;
        CreatedBy = createdBy;
    }


    public override void SetId()
    {
        Id = Guid.NewGuid().ToString();
    }

    public void Update(Information name, DateTime lastModified, User modifiedBy)
    {
        Name = name;
        LastModified = lastModified;
        ModifiedBy = modifiedBy;
    }
}
[BsonIgnoreExtraElements]
public class User
{
    public string UserId { get; set; } = string.Empty;

}
[BsonIgnoreExtraElements]
public class Information
{
    public string Text { get; set; } = string.Empty;

}